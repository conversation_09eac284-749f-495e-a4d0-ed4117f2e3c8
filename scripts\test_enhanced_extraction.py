#!/usr/bin/env python3
"""
Test script for enhanced CUFE extraction functionality
"""

import os
import sys
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.extraction_service.main import _extract_cufe_from_xml
from shared.schemas.extraction import CUFEData

def test_extraction_with_sample_xml():
    """Test enhanced extraction with sample XML files"""
    
    # Find sample XML files
    extracted_files_dir = project_root / "extracted_files"
    xml_files = []
    
    for subdir in extracted_files_dir.iterdir():
        if subdir.is_dir():
            for file in subdir.iterdir():
                if file.suffix.lower() == '.xml':
                    xml_files.append(file)
    
    if not xml_files:
        print("No XML files found in extracted_files directory")
        return
    
    print(f"Found {len(xml_files)} XML files to test")
    print("=" * 60)
    
    for i, xml_file in enumerate(xml_files[:3], 1):  # Test first 3 files
        print(f"\nTest {i}: {xml_file.name}")
        print("-" * 40)
        
        try:
            # Extract CUFE and comprehensive data
            cufe_data = _extract_cufe_from_xml(str(xml_file), extract_additional_data=True)
            
            print(f"✓ CUFE Value: {cufe_data.cufe_value}")
            print(f"✓ Issuer Name: {cufe_data.issuer_name}")
            print(f"✓ Document Number: {cufe_data.document_number}")
            print(f"✓ Issue Date: {cufe_data.issue_date}")
            print(f"✓ Total Amount: {cufe_data.total_amount}")
            
            # Enhanced tax details
            print("\nTax Details:")
            print(f"  - Tax Exclusive Amount: {cufe_data.tax_exclusive_amount}")
            print(f"  - Tax Inclusive Amount: {cufe_data.tax_inclusive_amount}")
            print(f"  - Total Tax Amount: {cufe_data.total_tax_amount}")
            print(f"  - IVA Amount: {cufe_data.iva_amount}")
            print(f"  - Rete Fuente Amount: {cufe_data.rete_fuente_amount}")
            print(f"  - Rete IVA Amount: {cufe_data.rete_iva_amount}")
            print(f"  - Rete ICA Amount: {cufe_data.rete_ica_amount}")
            
            # Additional details
            print(f"\nAdditional Details:")
            print(f"  - Currency Code: {cufe_data.currency_code}")
            print(f"  - Invoice Type Code: {cufe_data.invoice_type_code}")
            print(f"  - Due Date: {cufe_data.due_date}")
            print(f"  - Payable Amount: {cufe_data.payable_amount}")
            
            # Line items
            if cufe_data.line_items:
                print(f"\nLine Items ({len(cufe_data.line_items)}):")
                for j, item in enumerate(cufe_data.line_items[:3], 1):  # Show first 3 items
                    print(f"  Item {j}:")
                    print(f"    - Name: {item.item_name}")
                    print(f"    - Description: {item.item_description}")
                    print(f"    - Quantity: {item.invoiced_quantity}")
                    print(f"    - Unit Price: {item.unit_price}")
                    print(f"    - Line Extension: {item.line_extension_amount}")
                if len(cufe_data.line_items) > 3:
                    print(f"    ... and {len(cufe_data.line_items) - 3} more items")
            else:
                print("\nLine Items: None found")
            
            # Allowance charges
            if cufe_data.allowance_charges:
                print(f"\nAllowance/Charges ({len(cufe_data.allowance_charges)}):")
                for j, ac in enumerate(cufe_data.allowance_charges, 1):
                    charge_type = "Charge" if ac.charge_indicator else "Allowance"
                    print(f"  {charge_type} {j}: {ac.amount} ({ac.allowance_charge_reason})")
            else:
                print("\nAllowance/Charges: None found")
            
            # Payment terms
            if cufe_data.payment_terms:
                print(f"\nPayment Terms ({len(cufe_data.payment_terms)}):")
                for j, pt in enumerate(cufe_data.payment_terms, 1):
                    print(f"  Term {j}:")
                    print(f"    - Payment Means Code: {pt.payment_means_code}")
                    print(f"    - Due Date: {pt.payment_due_date}")
                    print(f"    - Note: {pt.payment_terms_note}")
            else:
                print("\nPayment Terms: None found")
            
            print(f"\n✓ Successfully extracted comprehensive data from {xml_file.name}")
            
        except Exception as e:
            print(f"✗ Error extracting from {xml_file.name}: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("=" * 60)

def test_extraction_completeness():
    """Test that all expected fields are being extracted"""
    
    extracted_files_dir = project_root / "extracted_files"
    xml_files = []
    
    for subdir in extracted_files_dir.iterdir():
        if subdir.is_dir():
            for file in subdir.iterdir():
                if file.suffix.lower() == '.xml':
                    xml_files.append(file)
                    break  # Just get one file per directory
    
    if not xml_files:
        print("No XML files found for completeness test")
        return
    
    print("\nCompleteness Test")
    print("=" * 60)
    
    # Test with first available XML file
    xml_file = xml_files[0]
    print(f"Testing completeness with: {xml_file.name}")
    
    try:
        cufe_data = _extract_cufe_from_xml(str(xml_file), extract_additional_data=True)
        
        # Check all expected fields
        fields_to_check = [
            'cufe_value', 'issuer_name', 'document_number', 'issue_date', 'total_amount',
            'tax_exclusive_amount', 'tax_inclusive_amount', 'total_tax_amount',
            'iva_amount', 'currency_code', 'invoice_type_code'
        ]
        
        print("\nField Extraction Status:")
        for field in fields_to_check:
            value = getattr(cufe_data, field, None)
            status = "✓" if value is not None else "✗"
            print(f"  {status} {field}: {value}")
        
        # Check collections
        collections = [
            ('line_items', cufe_data.line_items),
            ('allowance_charges', cufe_data.allowance_charges),
            ('payment_terms', cufe_data.payment_terms)
        ]
        
        print("\nCollection Extraction Status:")
        for name, collection in collections:
            if collection:
                print(f"  ✓ {name}: {len(collection)} items")
            else:
                print(f"  ✗ {name}: None")
        
        print(f"\n✓ Completeness test completed for {xml_file.name}")
        
    except Exception as e:
        print(f"✗ Error in completeness test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Enhanced CUFE Extraction Test")
    print("=" * 60)
    
    test_extraction_with_sample_xml()
    test_extraction_completeness()
    
    print("\nTest completed!")
