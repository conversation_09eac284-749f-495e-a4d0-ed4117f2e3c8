#!/usr/bin/env python3
"""
Script de monitoreo del sistema de procesamiento de facturas
"""

import os
import sys
import requests
import time
from pathlib import Path
import json
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_service_health():
    """Verificar salud de todos los servicios"""
    services = [
        ('API Service', 'http://localhost:8000/health'),
        ('Mailbox Service', 'http://localhost:8001/health'),
        ('File Processing Service', 'http://localhost:8002/health'),
        ('Extraction Service', 'http://localhost:8003/health')
    ]
    
    print(f"=== ESTADO DE SERVICIOS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
    
    service_status = {}
    for service_name, health_url in services:
        try:
            response = requests.get(health_url, timeout=3)
            if response.status_code == 200:
                status = "✅ ACTIVO"
                service_status[service_name] = True
            else:
                status = f"❌ ERROR {response.status_code}"
                service_status[service_name] = False
        except Exception as e:
            status = f"❌ NO DISPONIBLE"
            service_status[service_name] = False
        
        print(f"  {service_name}: {status}")
    
    return service_status

def check_file_system():
    """Verificar estado del sistema de archivos"""
    print("\n=== ESTADO DEL SISTEMA DE ARCHIVOS ===")
    
    # Contar archivos en diferentes directorios
    temp_files_path = Path('temp_files')
    processed_files_path = Path('processed_files/extracted_files')
    logs_path = Path('logs')
    
    stats = {}
    
    # Archivos ZIP pendientes
    if temp_files_path.exists():
        zip_files = list(temp_files_path.glob('*.zip'))
        stats['zip_files'] = len(zip_files)
        print(f"  Archivos ZIP pendientes: {len(zip_files)}")
    else:
        stats['zip_files'] = 0
        print("  Directorio temp_files: ❌ NO EXISTE")
    
    # Archivos extraídos
    if processed_files_path.exists():
        extracted_dirs = list(processed_files_path.iterdir())
        total_extracted = sum(len(list(d.iterdir())) for d in extracted_dirs if d.is_dir())
        stats['extracted_files'] = total_extracted
        stats['extraction_dirs'] = len(extracted_dirs)
        print(f"  Directorios de extracción: {len(extracted_dirs)}")
        print(f"  Archivos extraídos: {total_extracted}")
    else:
        stats['extracted_files'] = 0
        stats['extraction_dirs'] = 0
        print("  Directorio de archivos extraídos: ❌ NO EXISTE")
    
    # Logs
    if logs_path.exists():
        log_files = list(logs_path.glob('*.log'))
        stats['log_files'] = len(log_files)
        print(f"  Archivos de log: {len(log_files)}")
    else:
        stats['log_files'] = 0
        print("  Directorio de logs: ❌ NO EXISTE")
    
    return stats

def check_database_status():
    """Verificar estado de la base de datos"""
    print("\n=== ESTADO DE LA BASE DE DATOS ===")
    
    try:
        # Intentar obtener estadísticas de la API
        response = requests.get('http://localhost:8000/stats', timeout=5)
        
        if response.status_code == 200:
            stats = response.json()
            print("  ✅ Base de datos accesible")
            print(f"  Registros de email: {stats.get('total_emails', 'N/A')}")
            print(f"  Registros CUFE: {stats.get('total_cufe_records', 'N/A')}")
            print(f"  Archivos ZIP: {stats.get('total_zip_files', 'N/A')}")
            return True
        else:
            print(f"  ❌ Error accediendo a estadísticas: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ No se puede acceder a la base de datos: {e}")
        return False

def generate_report():
    """Generar reporte completo del sistema"""
    print("=" * 60)
    print("REPORTE DE MONITOREO DEL SISTEMA")
    print("=" * 60)
    
    # 1. Estado de servicios
    service_status = check_service_health()
    
    # 2. Estado del sistema de archivos
    file_stats = check_file_system()
    
    # 3. Estado de la base de datos
    db_status = check_database_status()
    
    # 4. Resumen y recomendaciones
    print("\n=== RESUMEN Y RECOMENDACIONES ===")
    
    all_services_active = all(service_status.values())
    
    if all_services_active:
        print("  ✅ Todos los servicios están activos")
    else:
        inactive_services = [name for name, status in service_status.items() if not status]
        print(f"  ❌ Servicios inactivos: {', '.join(inactive_services)}")
        print("  💡 Recomendación: Ejecutar 'python start_all_services.py'")
    
    if file_stats.get('zip_files', 0) > 0:
        print(f"  📁 Hay {file_stats['zip_files']} archivos ZIP pendientes de procesar")
        if all_services_active:
            print("  💡 Recomendación: Ejecutar 'python process_pending_files.py'")
    
    if file_stats.get('extracted_files', 0) == 0 and file_stats.get('zip_files', 0) > 0:
        print("  ⚠️  No hay archivos extraídos pero sí archivos ZIP pendientes")
        print("  💡 Esto indica que el procesamiento no se está ejecutando")
    
    if not db_status:
        print("  ❌ Problemas de conectividad con la base de datos")
        print("  💡 Verificar configuración de PostgreSQL")
    
    # 5. Guardar reporte en archivo
    report_file = f"logs/system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    os.makedirs('logs', exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"Reporte del Sistema - {datetime.now()}\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Servicios activos: {sum(service_status.values())}/{len(service_status)}\n")
        f.write(f"Archivos ZIP pendientes: {file_stats.get('zip_files', 0)}\n")
        f.write(f"Archivos extraídos: {file_stats.get('extracted_files', 0)}\n")
        f.write(f"Base de datos accesible: {'Sí' if db_status else 'No'}\n")
    
    print(f"\n📄 Reporte guardado en: {report_file}")

def continuous_monitoring():
    """Monitoreo continuo del sistema"""
    print("=== INICIANDO MONITOREO CONTINUO ===")
    print("Presiona Ctrl+C para detener")
    
    try:
        while True:
            generate_report()
            print(f"\n⏰ Próxima verificación en 60 segundos...")
            time.sleep(60)
    except KeyboardInterrupt:
        print("\n🛑 Monitoreo detenido por el usuario")

def main():
    """Función principal"""
    if len(sys.argv) > 1 and sys.argv[1] == '--continuous':
        continuous_monitoring()
    else:
        generate_report()

if __name__ == "__main__":
    main()
