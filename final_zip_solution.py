#!/usr/bin/env python3
"""
Solución final para el problema de detección de archivos ZIP en emails
"""

import os
import sys
import requests
import json
from pathlib import Path

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def start_services():
    """Iniciar todos los servicios necesarios"""
    print("=== INICIANDO SERVICIOS ===")
    
    services_to_check = [
        ('API Service', 'http://localhost:8000/health'),
        ('Mailbox Service', 'http://localhost:8001/health'),
        ('File Processing Service', 'http://localhost:8002/health'),
        ('Extraction Service', 'http://localhost:8003/health')
    ]
    
    active_services = 0
    for service_name, health_url in services_to_check:
        try:
            response = requests.get(health_url, timeout=3)
            if response.status_code == 200:
                print(f"✅ {service_name}: Activo")
                active_services += 1
            else:
                print(f"❌ {service_name}: Error {response.status_code}")
        except:
            print(f"❌ {service_name}: No disponible")
    
    if active_services == len(services_to_check):
        print(f"✅ Todos los servicios están activos ({active_services}/{len(services_to_check)})")
        return True
    else:
        print(f"⚠️  Solo {active_services}/{len(services_to_check)} servicios están activos")
        print("\n🔧 Para iniciar todos los servicios:")
        print("   python start_all_services.py")
        return False

def process_test_zips_directly():
    """Procesar los archivos ZIP de prueba directamente usando los servicios"""
    print("\n=== PROCESANDO ARCHIVOS ZIP DE PRUEBA ===")
    
    temp_files_path = Path('temp_files')
    zip_files = list(temp_files_path.glob('*.zip'))
    
    if not zip_files:
        print("❌ No hay archivos ZIP para procesar")
        return False
    
    print(f"📦 Encontrados {len(zip_files)} archivos ZIP")
    
    processed_count = 0
    cufe_count = 0
    
    for i, zip_file in enumerate(zip_files, 1):
        print(f"\n[{i}/{len(zip_files)}] Procesando: {zip_file.name}")
        
        try:
            # 1. Procesar ZIP con file processing service
            process_response = requests.post(
                'http://localhost:8002/process-zip',
                json={
                    'file_path': str(zip_file),
                    'preserve_structure': True
                },
                timeout=30
            )
            
            if process_response.status_code == 200:
                process_result = process_response.json()
                xml_files = process_result.get('xml_files', [])
                print(f"   ✅ ZIP procesado: {len(xml_files)} archivos XML extraídos")
                
                # 2. Extraer CUFE de cada archivo XML
                for xml_file in xml_files:
                    try:
                        extraction_response = requests.post(
                            'http://localhost:8003/extract-cufe',
                            json={
                                'xml_file_path': xml_file['file_path'],
                                'extract_additional_data': True
                            },
                            timeout=30
                        )
                        
                        if extraction_response.status_code == 200:
                            extraction_result = extraction_response.json()
                            if extraction_result.get('success'):
                                cufe_value = extraction_result.get('cufe_value', '')
                                print(f"   🎯 CUFE extraído: {cufe_value[:20]}...")
                                cufe_count += 1
                            else:
                                print(f"   ❌ Error extrayendo CUFE: {extraction_result.get('message')}")
                        else:
                            print(f"   ❌ Error en servicio de extracción: {extraction_response.status_code}")
                            
                    except Exception as e:
                        print(f"   ❌ Error procesando XML: {e}")
                
                processed_count += 1
                
            else:
                print(f"   ❌ Error procesando ZIP: {process_response.status_code}")
                print(f"       Respuesta: {process_response.text}")
                
        except Exception as e:
            print(f"   ❌ Error general: {e}")
    
    print(f"\n📊 RESUMEN:")
    print(f"   Archivos ZIP procesados: {processed_count}/{len(zip_files)}")
    print(f"   CUFEs extraídos: {cufe_count}")
    
    return cufe_count > 0

def test_email_processing_with_mock():
    """Probar procesamiento de emails usando datos simulados"""
    print("\n=== PROBANDO PROCESAMIENTO DE EMAILS (SIMULADO) ===")
    
    # Crear un request de prueba que no requiere credenciales reales
    # pero que active la lógica de detección
    test_request = {
        "email_host": "test.example.com",
        "email_port": 993,
        "email_username": "<EMAIL>",
        "email_password": "test_password",
        "use_ssl": True,
        "folder": "INBOX",
        "date_from": "2025-09-16",
        "date_to": "2025-09-17",
        "max_emails": 5,
        "client_id": 1,
        "zip_only": False  # Procesar todos para ver el comportamiento
    }
    
    print("⚠️  NOTA: Esta es una prueba con credenciales simuladas")
    print("Para probar con emails reales, configura credenciales válidas")
    
    try:
        response = requests.post(
            'http://localhost:8001/process-emails',
            json=test_request,
            timeout=30
        )
        
        print(f"📡 Respuesta del servicio: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Servicio respondió correctamente:")
            print(f"   Emails procesados: {result.get('processed_count', 0)}")
            print(f"   Emails con ZIP: {result.get('emails_with_zip_attachments', 0)}")
        else:
            print(f"⚠️  Error esperado (credenciales simuladas): {response.status_code}")
            print("   Esto es normal con credenciales de prueba")
            
    except Exception as e:
        print(f"⚠️  Error esperado: {e}")
        print("   Esto es normal sin credenciales reales")

def create_final_instructions():
    """Crear instrucciones finales para el usuario"""
    print("\n" + "="*60)
    print("INSTRUCCIONES FINALES PARA RESOLVER EL PROBLEMA")
    print("="*60)
    
    print("\n🎯 PROBLEMA IDENTIFICADO:")
    print("   El sistema NO está detectando archivos ZIP en emails reales")
    print("   La lógica de detección funciona correctamente (probado en simulación)")
    
    print("\n✅ SOLUCIONES IMPLEMENTADAS:")
    print("   1. Logging detallado habilitado (LOG_ZIP_DETECTION=true)")
    print("   2. Detección de ZIP mejorada con más indicadores")
    print("   3. Método de detección alternativo más agresivo")
    print("   4. Archivos ZIP de prueba creados para testing")
    
    print("\n🔧 PASOS PARA RESOLVER:")
    print("   1. CONFIGURAR CREDENCIALES REALES:")
    print("      - Editar .env con credenciales de email válidas")
    print("      - O usar test_email_processing.py con credenciales reales")
    
    print("\n   2. INICIAR SERVICIOS:")
    print("      python start_all_services.py")
    
    print("\n   3. PROBAR CON EMAILS REALES:")
    print("      - Configurar credenciales en test_email_processing.py")
    print("      - Ejecutar: python test_email_processing.py")
    print("      - Revisar logs detallados para ver qué está detectando")
    
    print("\n   4. VERIFICAR CONFIGURACIÓN:")
    print("      - Asegurar que LOG_ZIP_DETECTION=true en .env")
    print("      - Verificar filtros de fecha no demasiado restrictivos")
    print("      - Confirmar que los emails realmente tienen archivos ZIP")
    
    print("\n📋 ARCHIVOS CREADOS/MODIFICADOS:")
    print("   - .env: Configuración mejorada con logging habilitado")
    print("   - services/mailbox_service/email_service.py: Detección mejorada")
    print("   - temp_files/: Archivos ZIP de prueba con CUFEs válidos")
    print("   - Scripts de diagnóstico y prueba")
    
    print("\n🔍 DIAGNÓSTICO ADICIONAL:")
    print("   Si el problema persiste después de configurar credenciales:")
    print("   1. Revisar logs del mailbox service para ver detección ZIP")
    print("   2. Verificar que los emails reales tienen archivos ZIP")
    print("   3. Probar con diferentes filtros de fecha")
    print("   4. Verificar conectividad IMAP")
    
    print("\n💡 NOTA IMPORTANTE:")
    print("   Los archivos ZIP en temp_files sugieren que en algún momento")
    print("   el sistema SÍ descargó archivos. El problema puede ser:")
    print("   - Cambio en configuración")
    print("   - Emails recientes sin archivos ZIP")
    print("   - Filtros de fecha muy restrictivos")

def main():
    """Función principal"""
    print("=" * 60)
    print("SOLUCIÓN FINAL - DETECCIÓN DE ARCHIVOS ZIP EN EMAILS")
    print("=" * 60)
    
    # 1. Verificar servicios
    services_active = start_services()
    
    if services_active:
        # 2. Probar procesamiento directo de ZIPs
        direct_success = process_test_zips_directly()
        
        # 3. Probar procesamiento de emails (simulado)
        test_email_processing_with_mock()
        
        if direct_success:
            print("\n🎉 ¡ÉXITO! El procesamiento de archivos ZIP funciona correctamente")
        else:
            print("\n⚠️  Hay problemas en el procesamiento de archivos ZIP")
    
    # 4. Mostrar instrucciones finales
    create_final_instructions()

if __name__ == "__main__":
    main()
