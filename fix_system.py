#!/usr/bin/env python3
"""
Script para corregir problemas identificados en el sistema
"""

import os
import shutil
from pathlib import Path

def fix_file_extensions():
    """Corregir extensiones de archivo incorrectas"""
    print("=== CORRIGIENDO EXTENSIONES DE ARCHIVO ===")
    
    temp_files_path = Path('temp_files')
    fixed_count = 0
    
    for zip_file in temp_files_path.glob('*.zip'):
        try:
            import zipfile
            with zipfile.ZipFile(zip_file, 'r') as zf:
                files_in_zip = zf.namelist()
                
                # Buscar archivos con extensión .xlm (debería ser .xml)
                xlm_files = [f for f in files_in_zip if f.lower().endswith('.xlm')]
                
                if xlm_files:
                    print(f"Archivo ZIP con extensión incorrecta: {zip_file.name}")
                    print(f"  Archivos .xlm encontrados: {xlm_files}")
                    
                    # Crear un nuevo ZIP con las extensiones corregidas
                    temp_zip_path = zip_file.with_suffix('.temp.zip')
                    
                    with zipfile.ZipFile(temp_zip_path, 'w') as new_zf:
                        for file_name in files_in_zip:
                            # Leer el contenido del archivo original
                            file_content = zf.read(file_name)
                            
                            # Corregir la extensión si es necesario
                            if file_name.lower().endswith('.xlm'):
                                new_file_name = file_name[:-4] + '.xml'
                                print(f"    Corrigiendo: {file_name} -> {new_file_name}")
                            else:
                                new_file_name = file_name
                            
                            # Escribir al nuevo ZIP
                            new_zf.writestr(new_file_name, file_content)
                    
                    # Reemplazar el archivo original
                    shutil.move(str(temp_zip_path), str(zip_file))
                    fixed_count += 1
                    print(f"  ✓ Corregido: {zip_file.name}")
                    
        except Exception as e:
            print(f"Error procesando {zip_file.name}: {e}")
    
    print(f"Total archivos corregidos: {fixed_count}")

def create_logs_directory():
    """Crear directorio de logs"""
    print("\n=== CREANDO DIRECTORIO DE LOGS ===")
    
    logs_dir = Path('logs')
    logs_dir.mkdir(exist_ok=True)
    print(f"✓ Directorio de logs creado: {logs_dir}")

def start_services():
    """Instrucciones para iniciar servicios"""
    print("\n=== INSTRUCCIONES PARA INICIAR SERVICIOS ===")
    print("Para iniciar todos los servicios, ejecuta los siguientes comandos en terminales separadas:")
    print()
    print("Terminal 1 - API Service:")
    print("  cd services/api_service")
    print("  python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload")
    print()
    print("Terminal 2 - Mailbox Service:")
    print("  cd services/mailbox_service")
    print("  python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload")
    print()
    print("Terminal 3 - File Processing Service:")
    print("  cd services/file_processing_service")
    print("  python -m uvicorn main:app --host 0.0.0.0 --port 8002 --reload")
    print()
    print("Terminal 4 - Extraction Service:")
    print("  cd services/extraction_service")
    print("  python -m uvicorn main:app --host 0.0.0.0 --port 8003 --reload")
    print()
    print("O usa el script start_all_services.py si existe")

def create_service_startup_script():
    """Crear script para iniciar todos los servicios"""
    print("\n=== CREANDO SCRIPT DE INICIO DE SERVICIOS ===")
    
    startup_script = """#!/usr/bin/env python3
\"\"\"
Script para iniciar todos los servicios del sistema
\"\"\"

import subprocess
import sys
import time
import os

def start_service(service_name, service_path, port):
    \"\"\"Iniciar un servicio específico\"\"\"
    print(f"Iniciando {service_name} en puerto {port}...")
    
    # Cambiar al directorio del servicio
    os.chdir(service_path)
    
    # Iniciar el servicio
    cmd = [
        sys.executable, "-m", "uvicorn", "main:app",
        "--host", "0.0.0.0",
        "--port", str(port),
        "--reload"
    ]
    
    process = subprocess.Popen(cmd)
    
    # Volver al directorio raíz
    os.chdir("../..")
    
    return process

def main():
    \"\"\"Iniciar todos los servicios\"\"\"
    services = [
        ("API Service", "services/api_service", 8000),
        ("Mailbox Service", "services/mailbox_service", 8001),
        ("File Processing Service", "services/file_processing_service", 8002),
        ("Extraction Service", "services/extraction_service", 8003)
    ]
    
    processes = []
    
    for service_name, service_path, port in services:
        try:
            process = start_service(service_name, service_path, port)
            processes.append((service_name, process))
            time.sleep(2)  # Esperar un poco entre servicios
        except Exception as e:
            print(f"Error iniciando {service_name}: {e}")
    
    print("\\nTodos los servicios iniciados. Presiona Ctrl+C para detener.")
    
    try:
        # Esperar indefinidamente
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\\nDeteniendo servicios...")
        for service_name, process in processes:
            print(f"Deteniendo {service_name}...")
            process.terminate()

if __name__ == "__main__":
    main()
"""
    
    with open('start_all_services.py', 'w', encoding='utf-8') as f:
        f.write(startup_script)
    
    print("✓ Script de inicio creado: start_all_services.py")

def main():
    """Función principal"""
    print("=== INICIANDO CORRECCIÓN DEL SISTEMA ===\\n")
    
    # 1. Crear directorio de logs
    create_logs_directory()
    
    # 2. Corregir extensiones de archivo
    fix_file_extensions()
    
    # 3. Crear script de inicio de servicios
    create_service_startup_script()
    
    # 4. Mostrar instrucciones
    start_services()
    
    print("\\n=== CORRECCIÓN COMPLETADA ===")
    print("\\nPróximos pasos:")
    print("1. Ejecutar: python start_all_services.py")
    print("2. Verificar que todos los servicios estén activos")
    print("3. Procesar los archivos ZIP pendientes")

if __name__ == "__main__":
    main()
