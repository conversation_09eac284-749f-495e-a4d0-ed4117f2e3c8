"""
Batch processing configuration for email service
"""

import os
from typing import Dict, Any

class BatchProcessingConfig:
    """Configuration class for batch processing settings"""
    
    # Default batch processing settings
    DEFAULT_BATCH_SIZE = 50
    DEFAULT_ZIP_FILTER_BATCH_SIZE = 100
    DEFAULT_MAX_NON_ZIP_EMAILS = 50
    DEFAULT_ENABLE_ZIP_FILTERING = True
    DEFAULT_ENABLE_PARALLEL_PROCESSING = False
    
    # Performance settings
    DEFAULT_CONNECTION_TIMEOUT = 30
    DEFAULT_FETCH_TIMEOUT = 10
    DEFAULT_MAX_RETRIES = 3
    
    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """
        Get batch processing configuration from environment variables or defaults
        
        Returns:
            Dictionary with configuration settings
        """
        return {
            # Batch processing settings
            "batch_size": int(os.getenv("EMAIL_BATCH_SIZE", cls.DEFAULT_BATCH_SIZE)),
            "zip_filter_batch_size": int(os.getenv("ZIP_FILTER_BATCH_SIZE", cls.DEFAULT_ZIP_FILTER_BATCH_SIZE)),
            "max_non_zip_emails": int(os.getenv("MAX_NON_ZIP_EMAILS", cls.DEFAULT_MAX_NON_ZIP_EMAILS)),
            "enable_zip_filtering": os.getenv("ENABLE_ZIP_FILTERING", "true").lower() == "true",
            "enable_parallel_processing": os.getenv("ENABLE_PARALLEL_PROCESSING", "false").lower() == "true",
            
            # Performance settings
            "connection_timeout": int(os.getenv("EMAIL_CONNECTION_TIMEOUT", cls.DEFAULT_CONNECTION_TIMEOUT)),
            "fetch_timeout": int(os.getenv("EMAIL_FETCH_TIMEOUT", cls.DEFAULT_FETCH_TIMEOUT)),
            "max_retries": int(os.getenv("EMAIL_MAX_RETRIES", cls.DEFAULT_MAX_RETRIES)),
            
            # Logging settings
            "log_batch_progress": os.getenv("LOG_BATCH_PROGRESS", "true").lower() == "true",
            "log_zip_detection": os.getenv("LOG_ZIP_DETECTION", "false").lower() == "true",
        }
    
    @classmethod
    def get_optimized_config_for_large_mailboxes(cls) -> Dict[str, Any]:
        """
        Get optimized configuration for large mailboxes (thousands of emails)
        
        Returns:
            Dictionary with optimized settings
        """
        config = cls.get_config()
        config.update({
            "batch_size": 100,
            "zip_filter_batch_size": 200,
            "max_non_zip_emails": 100,
            "enable_zip_filtering": True,
            "connection_timeout": 60,
            "fetch_timeout": 20,
        })
        return config
    
    @classmethod
    def get_fast_config_for_zip_only(cls) -> Dict[str, Any]:
        """
        Get configuration optimized for ZIP-only processing (fastest)
        
        Returns:
            Dictionary with ZIP-optimized settings
        """
        config = cls.get_config()
        config.update({
            "batch_size": 25,
            "zip_filter_batch_size": 50,
            "max_non_zip_emails": 0,  # Skip non-ZIP emails entirely
            "enable_zip_filtering": True,
            "log_zip_detection": True,
        })
        return config

# Environment variable documentation
ENV_VARS_DOCUMENTATION = """
Email Batch Processing Environment Variables:

Performance Settings:
- EMAIL_BATCH_SIZE: Number of emails to process in each batch (default: 50)
- ZIP_FILTER_BATCH_SIZE: Batch size for ZIP filtering phase (default: 100)
- MAX_NON_ZIP_EMAILS: Maximum non-ZIP emails to process for record keeping (default: 50)
- ENABLE_ZIP_FILTERING: Enable ZIP-only filtering for faster processing (default: true)
- ENABLE_PARALLEL_PROCESSING: Enable parallel processing (experimental, default: false)

Connection Settings:
- EMAIL_CONNECTION_TIMEOUT: IMAP connection timeout in seconds (default: 30)
- EMAIL_FETCH_TIMEOUT: Email fetch timeout in seconds (default: 10)
- EMAIL_MAX_RETRIES: Maximum retry attempts for failed operations (default: 3)

Logging Settings:
- LOG_BATCH_PROGRESS: Log batch processing progress (default: true)
- LOG_ZIP_DETECTION: Log ZIP attachment detection details (default: false)

Example .env configuration for high-performance ZIP processing:
EMAIL_BATCH_SIZE=25
ZIP_FILTER_BATCH_SIZE=50
MAX_NON_ZIP_EMAILS=0
ENABLE_ZIP_FILTERING=true
LOG_ZIP_DETECTION=true
"""
