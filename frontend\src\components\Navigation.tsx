import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

interface NavigationProps {
  title: string
  subtitle?: string
  children?: React.ReactNode
}

const Navigation: React.FC<NavigationProps> = ({ title, subtitle, children }) => {
  const { user, client, logout } = useAuth()
  const location = useLocation()

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/')
  }

  return (
    <header className="bg-white shadow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
            {subtitle && (
              <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
            )}
          </div>
          <div className="flex items-center space-x-4">
            {/* Navigation Links */}
            <nav className="flex items-center space-x-4">
              <Link
                to={user?.is_admin ? "/admin/dashboard" : "/dashboard"}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  (user?.is_admin && isActive('/admin/dashboard')) || (!user?.is_admin && isActive('/dashboard') && !isActive('/admin'))
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                Dashboard
              </Link>
              <Link
                to="/process"
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive('/process')
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                Process Invoices
              </Link>
              
              {/* Admin Links */}
              {user?.is_admin && (
                <>
                  <div className="h-6 w-px bg-gray-300" />
                  <Link
                    to="/admin/dashboard"
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive('/admin/dashboard') || (isActive('/admin') && location.pathname === '/admin')
                        ? 'bg-purple-100 text-purple-700'
                        : 'text-purple-600 hover:text-purple-900 hover:bg-purple-50'
                    }`}
                  >
                    Admin Dashboard
                  </Link>
                  <Link
                    to="/admin/users"
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive('/admin/users')
                        ? 'bg-purple-100 text-purple-700'
                        : 'text-purple-600 hover:text-purple-900 hover:bg-purple-50'
                    }`}
                  >
                    User Management
                  </Link>
                </>
              )}
            </nav>

            {/* User Info and Actions */}
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">
                  {user?.full_name || user?.username}
                  {user?.is_admin && (
                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      Admin
                    </span>
                  )}
                </p>
                <p className="text-xs text-gray-500">{client?.company_name}</p>
              </div>
              
              {/* Custom Action Buttons */}
              {children}
              
              <button
                onClick={logout}
                className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Navigation
