#!/usr/bin/env python3
"""
Migration script to add enhanced invoice fields to existing database
This script safely adds new columns and tables without dropping existing data
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.database.connection import engine
from shared.utils.logger import get_logger
from sqlalchemy import text, inspect

logger = get_logger(__name__)

def check_column_exists(table_name: str, column_name: str) -> bool:
    """Check if a column exists in a table"""
    try:
        inspector = inspect(engine)
        columns = [col['name'] for col in inspector.get_columns(table_name)]
        return column_name in columns
    except Exception as e:
        logger.error(f"Error checking column {column_name} in {table_name}: {str(e)}")
        return False

def check_table_exists(table_name: str) -> bool:
    """Check if a table exists"""
    try:
        inspector = inspect(engine)
        return table_name in inspector.get_table_names()
    except Exception as e:
        logger.error(f"Error checking table {table_name}: {str(e)}")
        return False

def add_cufe_record_fields():
    """Add new fields to cufe_records table"""
    logger.info("Adding enhanced fields to cufe_records table...")
    
    new_fields = [
        ("tax_exclusive_amount", "VARCHAR(50)"),
        ("tax_inclusive_amount", "VARCHAR(50)"),
        ("allowance_total_amount", "VARCHAR(50)"),
        ("charge_total_amount", "VARCHAR(50)"),
        ("prepaid_amount", "VARCHAR(50)"),
        ("payable_amount", "VARCHAR(50)"),
        ("total_tax_amount", "VARCHAR(50)"),
        ("iva_amount", "VARCHAR(50)"),
        ("rete_fuente_amount", "VARCHAR(50)"),
        ("rete_iva_amount", "VARCHAR(50)"),
        ("rete_ica_amount", "VARCHAR(50)"),
        ("due_date", "TIMESTAMP"),
        ("currency_code", "VARCHAR(10)"),
        ("invoice_type_code", "VARCHAR(10)"),
        ("accounting_cost", "VARCHAR(100)")
    ]
    
    with engine.connect() as conn:
        for field_name, field_type in new_fields:
            if not check_column_exists("cufe_records", field_name):
                try:
                    conn.execute(text(f"ALTER TABLE cufe_records ADD COLUMN {field_name} {field_type}"))
                    conn.commit()
                    logger.info(f"Added column {field_name} to cufe_records")
                except Exception as e:
                    logger.error(f"Failed to add column {field_name}: {str(e)}")
                    conn.rollback()
            else:
                logger.info(f"Column {field_name} already exists in cufe_records")

def create_invoice_line_items_table():
    """Create invoice_line_items table"""
    logger.info("Creating invoice_line_items table...")
    
    if check_table_exists("invoice_line_items"):
        logger.info("Table invoice_line_items already exists")
        return
    
    create_sql = """
    CREATE TABLE invoice_line_items (
        id SERIAL PRIMARY KEY,
        cufe_record_id INTEGER NOT NULL REFERENCES cufe_records(id),
        line_number INTEGER,
        item_name VARCHAR(500),
        item_description TEXT,
        item_code VARCHAR(100),
        invoiced_quantity VARCHAR(50),
        unit_of_measure VARCHAR(50),
        unit_price VARCHAR(50),
        line_extension_amount VARCHAR(50),
        line_tax_amount VARCHAR(50),
        line_tax_inclusive_amount VARCHAR(50),
        allowance_charge_amount VARCHAR(50),
        free_of_charge_indicator BOOLEAN DEFAULT FALSE
    )
    """
    
    try:
        with engine.connect() as conn:
            conn.execute(text(create_sql))
            conn.commit()
        logger.info("Created invoice_line_items table successfully")
    except Exception as e:
        logger.error(f"Failed to create invoice_line_items table: {str(e)}")

def create_invoice_allowance_charges_table():
    """Create invoice_allowance_charges table"""
    logger.info("Creating invoice_allowance_charges table...")
    
    if check_table_exists("invoice_allowance_charges"):
        logger.info("Table invoice_allowance_charges already exists")
        return
    
    create_sql = """
    CREATE TABLE invoice_allowance_charges (
        id SERIAL PRIMARY KEY,
        cufe_record_id INTEGER NOT NULL REFERENCES cufe_records(id),
        charge_indicator BOOLEAN NOT NULL,
        allowance_charge_reason_code VARCHAR(10),
        allowance_charge_reason VARCHAR(255),
        multiplier_factor_numeric VARCHAR(20),
        amount VARCHAR(50),
        base_amount VARCHAR(50),
        tax_category VARCHAR(50),
        tax_amount VARCHAR(50)
    )
    """
    
    try:
        with engine.connect() as conn:
            conn.execute(text(create_sql))
            conn.commit()
        logger.info("Created invoice_allowance_charges table successfully")
    except Exception as e:
        logger.error(f"Failed to create invoice_allowance_charges table: {str(e)}")

def create_invoice_payment_terms_table():
    """Create invoice_payment_terms table"""
    logger.info("Creating invoice_payment_terms table...")
    
    if check_table_exists("invoice_payment_terms"):
        logger.info("Table invoice_payment_terms already exists")
        return
    
    create_sql = """
    CREATE TABLE invoice_payment_terms (
        id SERIAL PRIMARY KEY,
        cufe_record_id INTEGER NOT NULL REFERENCES cufe_records(id),
        payment_means_code VARCHAR(10),
        payment_due_date TIMESTAMP,
        payment_terms_note TEXT,
        settlement_period_measure VARCHAR(20),
        settlement_period_unit VARCHAR(10),
        settlement_discount_percent VARCHAR(10),
        penalty_surcharge_percent VARCHAR(10),
        amount VARCHAR(50)
    )
    """
    
    try:
        with engine.connect() as conn:
            conn.execute(text(create_sql))
            conn.commit()
        logger.info("Created invoice_payment_terms table successfully")
    except Exception as e:
        logger.error(f"Failed to create invoice_payment_terms table: {str(e)}")

def run_migration():
    """Run the complete migration"""
    logger.info("Starting enhanced invoice fields migration...")
    
    try:
        # Add new fields to existing cufe_records table
        add_cufe_record_fields()
        
        # Create new tables
        create_invoice_line_items_table()
        create_invoice_allowance_charges_table()
        create_invoice_payment_terms_table()
        
        logger.info("Migration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        return False

def verify_migration():
    """Verify that migration was successful"""
    logger.info("Verifying migration...")
    
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        # Check required tables exist
        required_tables = [
            "cufe_records",
            "invoice_line_items", 
            "invoice_allowance_charges",
            "invoice_payment_terms"
        ]
        
        for table in required_tables:
            if table not in tables:
                logger.error(f"Table {table} not found after migration")
                return False
            logger.info(f"✓ Table {table} exists")
        
        # Check some key new columns in cufe_records
        cufe_columns = [col['name'] for col in inspector.get_columns("cufe_records")]
        required_columns = ["iva_amount", "rete_fuente_amount", "tax_exclusive_amount"]
        
        for column in required_columns:
            if column not in cufe_columns:
                logger.error(f"Column {column} not found in cufe_records after migration")
                return False
            logger.info(f"✓ Column {column} exists in cufe_records")
        
        logger.info("Migration verification successful!")
        return True
        
    except Exception as e:
        logger.error(f"Migration verification failed: {str(e)}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrate enhanced invoice fields")
    parser.add_argument(
        "--verify", 
        action="store_true", 
        help="Only verify migration was successful"
    )
    
    args = parser.parse_args()
    
    if args.verify:
        success = verify_migration()
    else:
        success = run_migration()
        if success:
            success = verify_migration()
    
    sys.exit(0 if success else 1)
