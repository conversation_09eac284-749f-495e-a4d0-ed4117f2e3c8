"""
Pydantic schemas for main API service
"""

from pydantic import BaseModel, EmailStr, validator
from typing import List, Optional
from datetime import datetime
from shared.schemas.extraction import (
    InvoiceLineItemData,
    InvoiceAllowanceChargeData,
    InvoicePaymentTermData,
    CompanyInformation
)

class ProcessEmailsRequest(BaseModel):
    """
    Request schema for processing emails through the complete pipeline with date-based filtering
    """
    email_host: str
    email_port: int = 993
    email_username: EmailStr
    email_password: str
    use_ssl: bool = True
    folder: str = "INBOX"

    # Date filtering (primary filtering method)
    date_filter: Optional[str] = None  # Format: "SINCE 01-Jan-2024" or "SINCE 01-Jan-2024 BEFORE 31-Jan-2024"
    start_date: Optional[datetime] = None  # Alternative: start date for filtering
    end_date: Optional[datetime] = None    # Alternative: end date for filtering

    # Legacy parameter (deprecated in favor of date filtering)
    max_emails: Optional[int] = None  # Deprecated: Use date filtering instead

    @validator('date_filter', pre=True, always=True)
    def build_date_filter_from_dates(cls, v, values):
        """
        Build date_filter from start_date and end_date if date_filter is not provided
        """
        if v is not None:
            return v

        start_date = values.get('start_date')
        end_date = values.get('end_date')

        if start_date or end_date:
            from services.mailbox_service.email_service import EmailService
            return EmailService.create_date_range_filter(start_date, end_date)

        return v

class CUFEResponse(BaseModel):
    """
    Response schema for comprehensive CUFE and invoice information
    """
    cufe_value: str
    email_id: str
    reception_date: datetime
    xml_file_path: str
    pdf_file_path: Optional[str] = None
    processed_date: datetime

    # Basic invoice information
    issuer_name: Optional[str] = None
    document_number: Optional[str] = None
    issue_date: Optional[datetime] = None
    total_amount: Optional[str] = None

    # Enhanced tax and monetary details
    tax_exclusive_amount: Optional[str] = None
    tax_inclusive_amount: Optional[str] = None
    allowance_total_amount: Optional[str] = None
    charge_total_amount: Optional[str] = None
    prepaid_amount: Optional[str] = None
    payable_amount: Optional[str] = None

    # Tax breakdown details
    total_tax_amount: Optional[str] = None
    iva_amount: Optional[str] = None
    rete_fuente_amount: Optional[str] = None
    rete_iva_amount: Optional[str] = None
    rete_ica_amount: Optional[str] = None

    # Additional invoice details
    due_date: Optional[datetime] = None
    currency_code: Optional[str] = None
    invoice_type_code: Optional[str] = None
    accounting_cost: Optional[str] = None

    # Company information - comprehensive business entity data
    issuer_company: Optional[CompanyInformation] = None  # Supplier/issuer company details
    customer_company: Optional[CompanyInformation] = None  # Customer/buyer company details

    # Related data collections
    line_items: Optional[List[InvoiceLineItemData]] = None
    allowance_charges: Optional[List[InvoiceAllowanceChargeData]] = None
    payment_terms: Optional[List[InvoicePaymentTermData]] = None

class CUFEListResponse(BaseModel):
    """
    Response schema for listing CUFE records
    """
    records: List[CUFEResponse]
    total: int
    skip: int
    limit: int

class EmailProcessingSummary(BaseModel):
    """
    Schema for detailed email processing summary
    """
    total_emails_processed: int = 0
    emails_with_zip_attachments: int = 0
    zip_files_downloaded: int = 0
    xml_files_extracted: int = 0
    cufe_records_created: int = 0
    sample_email_senders: Optional[List[str]] = None
    sample_email_subjects: Optional[List[str]] = None

class ProcessEmailsResponse(BaseModel):
    """
    Enhanced response schema for email processing
    """
    success: bool
    message: str
    status: str  # processing, completed, failed
    user: str
    processing_summary: Optional[EmailProcessingSummary] = None
    errors: Optional[List[str]] = None

class ProcessingStatus(BaseModel):
    """
    Schema for processing status information
    """
    status: str  # pending, processing, completed, failed
    message: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processed_emails: int = 0
    extracted_cufes: int = 0
    errors: Optional[List[str]] = None

class ServiceHealth(BaseModel):
    """
    Schema for service health information
    """
    service_name: str
    status: str  # healthy, unhealthy, degraded
    last_check: datetime
    response_time_ms: Optional[float] = None
    error_message: Optional[str] = None

class SystemStatus(BaseModel):
    """
    Schema for overall system status
    """
    overall_status: str
    services: List[ServiceHealth]
    last_updated: datetime
