#!/usr/bin/env python3
"""
Test script to verify the client_id fix is working
"""

import os
import sys
import requests
import json
from datetime import datetime

# Set the database URL environment variable
os.environ['DATABASE_URL'] = 'postgresql://cufe_user:cufe_password@localhost:5432/cufe_db'

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.database.models import EmailRecord, CUFERecord, Client, User
from shared.database.connection import get_db

def check_database_state():
    """Check the current state of the database"""
    print("🔍 Checking database state...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Check clients
        print("\n📊 CLIENTS:")
        clients = db.query(Client).all()
        for client in clients:
            print(f"  ID: {client.id}, Client ID: {client.client_id}, Company: {client.company_name}")
        
        # Check users
        print("\n👥 USERS:")
        users = db.query(User).all()
        for user in users:
            print(f"  ID: {user.id}, Username: {user.username}, Client ID: {user.client_id}")
        
        # Check recent email records
        print("\n📧 RECENT EMAIL RECORDS:")
        email_records = db.query(EmailRecord).order_by(EmailRecord.id.desc()).limit(5).all()
        for record in email_records:
            print(f"  ID: {record.id}, Email ID: {record.email_id[:20]}..., Client ID: {record.client_id}, Status: {record.processing_status}")
        
        # Check CUFE records with client association
        print("\n🧾 CUFE RECORDS WITH CLIENT INFO:")
        cufe_query = db.query(CUFERecord, EmailRecord.client_id).join(EmailRecord).order_by(CUFERecord.id.desc()).limit(3)
        cufe_records = cufe_query.all()
        for cufe_record, client_id in cufe_records:
            print(f"  CUFE: {cufe_record.cufe_value[:20]}..., Issuer: {cufe_record.issuer_name}, Client ID: {client_id}")
        
        # Count records without client_id
        print("\n⚠️  RECORDS WITHOUT CLIENT_ID:")
        emails_without_client = db.query(EmailRecord).filter(EmailRecord.client_id.is_(None)).count()
        print(f"  Email records without client_id: {emails_without_client}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking database state: {e}")
        return False
    finally:
        db.close()

def test_api_authentication():
    """Test API authentication and CUFE retrieval"""
    print("\n🔐 Testing API authentication...")
    
    try:
        # Login to get token
        login_response = requests.post(
            "http://localhost:8000/auth/login",
            json={"username": "testuser", "password": "password123"},
            timeout=10
        )
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            token = login_data["access_token"]
            user_info = login_data["user"]
            client_info = login_data["client"]
            
            print(f"✅ Login successful for user: {user_info['username']}")
            print(f"   Client: {client_info['company_name']} (ID: {user_info['client_id']})")
            
            # Test CUFE retrieval
            headers = {"Authorization": f"Bearer {token}"}
            cufe_response = requests.get(
                "http://localhost:8000/cufe/?skip=0&limit=10",
                headers=headers,
                timeout=10
            )
            
            if cufe_response.status_code == 200:
                cufe_data = cufe_response.json()
                print(f"✅ CUFE API call successful")
                print(f"   Total records: {cufe_data['total']}")
                print(f"   Records returned: {len(cufe_data['records'])}")
                
                if cufe_data['records']:
                    print("   Sample records:")
                    for record in cufe_data['records'][:2]:
                        print(f"     - CUFE: {record['cufe_value'][:20]}...")
                        print(f"       Issuer: {record.get('issuer_name', 'N/A')}")
                else:
                    print("   No CUFE records found for this client")
                
                return True
            else:
                print(f"❌ CUFE API call failed: {cufe_response.status_code}")
                return False
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing client_id fix...")
    
    # Check database state
    db_success = check_database_state()
    
    # Test API
    api_success = test_api_authentication()
    
    if db_success and api_success:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed")
        sys.exit(1)
