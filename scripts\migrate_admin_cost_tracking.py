#!/usr/bin/env python3
"""
Migration script to add admin cost tracking tables
This script safely adds new tables for LLM usage and deployment cost tracking
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shared.database.connection import engine
from shared.utils.logger import get_logger
from sqlalchemy import text, inspect

logger = get_logger(__name__)

def check_table_exists(table_name: str) -> bool:
    """Check if a table exists in the database"""
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        return table_name in tables
    except Exception as e:
        logger.error(f"Error checking if table {table_name} exists: {str(e)}")
        return False

def create_llm_usage_records_table():
    """Create the llm_usage_records table"""
    if check_table_exists("llm_usage_records"):
        logger.info("Table llm_usage_records already exists")
        return
    
    create_sql = """
    CREATE TABLE llm_usage_records (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        client_id INTEGER NOT NULL REFERENCES clients(id),
        provider VARCHAR(50) NOT NULL,
        model_name VARCHAR(100) NOT NULL,
        input_tokens INTEGER NOT NULL DEFAULT 0,
        output_tokens INTEGER NOT NULL DEFAULT 0,
        total_tokens INTEGER NOT NULL DEFAULT 0,
        input_cost VARCHAR(20) NOT NULL DEFAULT '0.00',
        output_cost VARCHAR(20) NOT NULL DEFAULT '0.00',
        total_cost VARCHAR(20) NOT NULL DEFAULT '0.00',
        request_type VARCHAR(100),
        processing_time_ms INTEGER,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE INDEX idx_llm_usage_user_id ON llm_usage_records(user_id);
    CREATE INDEX idx_llm_usage_client_id ON llm_usage_records(client_id);
    CREATE INDEX idx_llm_usage_created_at ON llm_usage_records(created_at);
    CREATE INDEX idx_llm_usage_provider ON llm_usage_records(provider);
    """
    
    try:
        with engine.connect() as conn:
            conn.execute(text(create_sql))
            conn.commit()
        logger.info("Created llm_usage_records table successfully")
    except Exception as e:
        logger.error(f"Failed to create llm_usage_records table: {str(e)}")

def create_deployment_cost_records_table():
    """Create the deployment_cost_records table"""
    if check_table_exists("deployment_cost_records"):
        logger.info("Table deployment_cost_records already exists")
        return
    
    create_sql = """
    CREATE TABLE deployment_cost_records (
        id SERIAL PRIMARY KEY,
        service_name VARCHAR(100) NOT NULL,
        cost_type VARCHAR(50) NOT NULL,
        cost_amount VARCHAR(20) NOT NULL,
        currency VARCHAR(10) DEFAULT 'USD',
        period_start TIMESTAMP NOT NULL,
        period_end TIMESTAMP NOT NULL,
        billing_period VARCHAR(20) NOT NULL,
        resource_usage TEXT,
        resource_units VARCHAR(50),
        provider VARCHAR(100),
        region VARCHAR(50),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE INDEX idx_deployment_cost_service ON deployment_cost_records(service_name);
    CREATE INDEX idx_deployment_cost_type ON deployment_cost_records(cost_type);
    CREATE INDEX idx_deployment_cost_period ON deployment_cost_records(period_start, period_end);
    CREATE INDEX idx_deployment_cost_created_at ON deployment_cost_records(created_at);
    """
    
    try:
        with engine.connect() as conn:
            conn.execute(text(create_sql))
            conn.commit()
        logger.info("Created deployment_cost_records table successfully")
    except Exception as e:
        logger.error(f"Failed to create deployment_cost_records table: {str(e)}")

def run_migration():
    """Run the complete migration"""
    logger.info("Starting admin cost tracking migration...")
    
    try:
        # Create new tables
        create_llm_usage_records_table()
        create_deployment_cost_records_table()
        
        logger.info("Admin cost tracking migration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        return False

def main():
    """Main function"""
    print("🚀 Running Admin Cost Tracking Migration")
    print("=" * 50)
    
    success = run_migration()
    
    if success:
        print("✅ Migration completed successfully!")
    else:
        print("❌ Migration failed. Check logs for details.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
