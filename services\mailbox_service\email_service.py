"""
Email connection and processing service
"""

import imaplib
import email
import os
import uuid
from datetime import datetime, timedelta
from typing import List, Tuple, Optional, Dict, Any
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
import ssl

from sqlalchemy.orm import Session
from shared.database.models import EmailRecord, ZipFileRecord
from shared.schemas.mailbox import EmailProcessRequest, DownloadedFile, EmailInfo
from shared.utils.logger import get_logger
from shared.utils.file_utils import ensure_directory, get_file_size, safe_filename
from shared.utils.date_utils import DateRangeHelper
from batch_config import BatchProcessingConfig

logger = get_logger(__name__)

class EmailService:
    """
    Service for connecting to email servers and processing emails
    """
    
    def __init__(self, temp_files_path: str = "./temp_files"):
        self.temp_files_path = temp_files_path
        ensure_directory(temp_files_path)

        # Load batch processing configuration
        self.config = BatchProcessingConfig.get_config()
        logger.info(f"Email service initialized with batch config: batch_size={self.config['batch_size']}, zip_filtering={self.config['enable_zip_filtering']}")

    def _validate_date_format(self, date_str: str) -> bool:
        """
        Validate IMAP date format (DD-MMM-YYYY)

        Args:
            date_str: Date string to validate

        Returns:
            True if valid, False otherwise
        """
        return DateRangeHelper.validate_imap_date_format(date_str)

    def _build_date_filter_criteria(self, date_filter: Optional[str]) -> List[str]:
        """
        Build IMAP date filter criteria from date_filter string with validation

        Args:
            date_filter: Date filter string (e.g., "SINCE 01-Jan-2024", "SINCE 01-Jan-2024 BEFORE 31-Jan-2024")

        Returns:
            List of IMAP search criteria

        Raises:
            ValueError: If date format is invalid
        """
        if not date_filter:
            return []

        # Split the date filter to handle multiple criteria
        criteria = []
        parts = date_filter.strip().split()

        i = 0
        while i < len(parts):
            if parts[i].upper() in ['SINCE', 'BEFORE', 'ON']:
                if i + 1 < len(parts):
                    date_part = parts[i + 1]
                    # Validate date format
                    if not self._validate_date_format(date_part):
                        raise ValueError(f"Invalid date format '{date_part}'. Expected format: DD-MMM-YYYY (e.g., 01-Jan-2024)")

                    criteria.append(f"{parts[i].upper()} {date_part}")
                    i += 2
                else:
                    raise ValueError(f"Missing date after '{parts[i]}' in date filter: {date_filter}")
            else:
                i += 1

        if not criteria:
            # If no recognized keywords, treat the entire string as a single criterion
            # But validate it first
            if not self._validate_date_format(date_filter):
                raise ValueError(f"Invalid date filter format: {date_filter}. Expected format: 'SINCE DD-MMM-YYYY' or 'BEFORE DD-MMM-YYYY'")
            criteria.append(date_filter)

        # Validate date range logic
        self._validate_date_range_logic(criteria)

        logger.info(f"Built date filter criteria: {criteria}")
        return criteria

    def _validate_date_range_logic(self, criteria: List[str]) -> None:
        """
        Validate that date range logic makes sense (SINCE date should be before BEFORE date)

        Args:
            criteria: List of date criteria

        Raises:
            ValueError: If date range logic is invalid
        """
        since_date = None
        before_date = None

        for criterion in criteria:
            parts = criterion.split()
            if len(parts) >= 2:
                command = parts[0].upper()
                date_str = parts[1]

                try:
                    date_obj = DateRangeHelper.parse_imap_date(date_str)

                    if command == 'SINCE':
                        since_date = date_obj
                    elif command == 'BEFORE':
                        before_date = date_obj
                except ValueError:
                    continue  # Skip invalid dates (already validated above)

        # Check if SINCE date is after BEFORE date
        if since_date and before_date and since_date >= before_date:
            raise ValueError(f"Invalid date range: SINCE date ({since_date.strftime('%d-%b-%Y')}) must be before BEFORE date ({before_date.strftime('%d-%b-%Y')})")

    @staticmethod
    def create_date_range_filter(start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> str:
        """
        Create an IMAP date filter string from datetime objects

        Args:
            start_date: Start date for filtering (inclusive)
            end_date: End date for filtering (exclusive)

        Returns:
            IMAP date filter string
        """
        return DateRangeHelper.create_imap_date_filter(start_date, end_date)
    
    def connect_to_mailbox(self, request: EmailProcessRequest) -> imaplib.IMAP4_SSL:
        """
        Connect to email server using IMAP
        
        Args:
            request: Email connection parameters
            
        Returns:
            IMAP connection object
            
        Raises:
            Exception: If connection fails
        """
        try:
            logger.info(f"Connecting to {request.email_host}:{request.email_port}")
            
            if request.use_ssl:
                # Create SSL context
                context = ssl.create_default_context()
                mail = imaplib.IMAP4_SSL(request.email_host, request.email_port, ssl_context=context)
            else:
                mail = imaplib.IMAP4(request.email_host, request.email_port)
            
            # Login
            mail.login(request.email_username, request.email_password)
            logger.info("Successfully connected to mailbox")
            
            return mail
            
        except Exception as e:
            logger.error(f"Failed to connect to mailbox: {str(e)}")
            raise Exception(f"Email connection failed: {str(e)}")
    
    def search_emails_with_attachments(
        self,
        mail: imaplib.IMAP4_SSL,
        request: EmailProcessRequest
    ) -> List[bytes]:
        """
        Search for emails with attachments

        Args:
            mail: IMAP connection
            request: Email processing request

        Returns:
            List of email IDs
        """
        try:
            # Select folder
            mail.select(request.folder)

            # Build search criteria - optimize for emails with attachments
            search_criteria = ['ALL']

            # Add date filtering criteria with validation
            try:
                date_criteria = self._build_date_filter_criteria(request.date_filter)
                search_criteria.extend(date_criteria)
            except ValueError as e:
                logger.error(f"Date filter validation error: {str(e)}")
                raise Exception(f"Invalid date filter: {str(e)}")

            # Log search criteria for debugging
            logger.info(f"IMAP search criteria: {search_criteria}")

            # Search for emails
            status, messages = mail.search(None, *search_criteria)

            if status != 'OK':
                raise Exception("Failed to search emails")

            email_ids = messages[0].split()

            # Debug logging to understand email ID types
            if email_ids:
                logger.debug(f"Email ID type: {type(email_ids[0])}, sample: {email_ids[0]}")

            # Enhanced logging with date filter information
            if request.date_filter:
                logger.info(f"Found {len(email_ids)} emails matching date filter '{request.date_filter}'")
            else:
                logger.info(f"Found {len(email_ids)} emails (no date filter applied)")

            return email_ids

        except Exception as e:
            logger.error(f"Failed to search emails: {str(e)}")
            raise

    def batch_filter_emails_with_zip_attachments(
        self,
        mail: imaplib.IMAP4_SSL,
        email_ids: List[bytes],
        batch_size: int = 50
    ) -> List[bytes]:
        """
        Efficiently filter emails to find only those with ZIP attachments using batch processing

        Args:
            mail: IMAP connection
            email_ids: List of all email IDs to check
            batch_size: Number of emails to process in each batch

        Returns:
            List of email IDs that have ZIP attachments
        """
        zip_email_ids = []
        total_emails = len(email_ids)

        # FORZAR logging detallado
        logger.info(f"🔍 BATCH FILTERING INICIADO: {total_emails} emails para revisar ZIP (batch size: {batch_size})")
        logger.info(f"🔍 CONFIGURACIÓN LOG_ZIP_DETECTION: {self.config.get('log_zip_detection', False)}")

        try:
            for i in range(0, total_emails, batch_size):
                batch = email_ids[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                total_batches = (total_emails + batch_size - 1) // batch_size

                logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} emails)")

                # Process batch to find ZIP attachments
                batch_zip_emails = self._check_batch_for_zip_attachments(mail, batch)
                zip_email_ids.extend(batch_zip_emails)

                logger.info(f"Batch {batch_num}: Found {len(batch_zip_emails)} emails with ZIP attachments")

            logger.info(f"Batch filtering complete: {len(zip_email_ids)}/{total_emails} emails have ZIP attachments")
            return zip_email_ids

        except Exception as e:
            logger.error(f"Failed to batch filter emails: {str(e)}")
            raise

    def _check_batch_for_zip_attachments(self, mail: imaplib.IMAP4_SSL, email_ids: List[bytes]) -> List[bytes]:
        """
        Check a batch of emails for ZIP attachments using efficient header-only fetching

        Args:
            mail: IMAP connection
            email_ids: Batch of email IDs to check

        Returns:
            List of email IDs that have ZIP attachments
        """
        zip_email_ids = []

        try:
            logger.info(f"🔍 REVISANDO BATCH: {len(email_ids)} emails para detección ZIP")

            for email_id in email_ids:
                try:
                    # Convert email_id to string for IMAP fetch (handle both bytes and int)
                    if isinstance(email_id, bytes):
                        email_id_str = email_id.decode()
                        email_id_for_logging = email_id_str
                    elif isinstance(email_id, int):
                        email_id_str = str(email_id)
                        email_id_for_logging = email_id_str
                    else:
                        email_id_str = str(email_id)
                        email_id_for_logging = str(email_id)

                    # First, do a quick check using BODYSTRUCTURE (more efficient than full fetch)
                    status, msg_data = mail.fetch(email_id_str, '(BODYSTRUCTURE)')

                    # Always log the raw IMAP response for debugging
                    logger.info(f"Email {email_id_for_logging}: IMAP fetch status: {status}, msg_data type: {type(msg_data)}, length: {len(msg_data) if msg_data else 0}")

                    if status == 'OK' and msg_data and msg_data[0]:
                        # Handle both bytes and non-bytes data from IMAP response
                        bodystructure_data = msg_data[0][1]

                        # Always log bodystructure data for debugging
                        logger.info(f"Email {email_id_for_logging}: Raw bodystructure type: {type(bodystructure_data).__name__}, value: {str(bodystructure_data)[:200]}...")

                        if isinstance(bodystructure_data, bytes):
                            bodystructure = bodystructure_data.decode('utf-8', errors='ignore')
                        else:
                            # Handle case where IMAP returns non-bytes data (e.g., integer)
                            # This can happen with certain IMAP server responses
                            logger.info(f"Email {email_id_for_logging}: BODYSTRUCTURE returned {type(bodystructure_data).__name__} instead of bytes: {bodystructure_data}")
                            bodystructure = str(bodystructure_data)

                        # Quick check for ZIP files in body structure (only if we have valid string data)
                        if bodystructure:
                            logger.info(f"🔍 EJECUTANDO DETECCIÓN ZIP para email {email_id_for_logging}")
                            has_zip_in_body = self._has_zip_in_bodystructure(bodystructure)
                            logger.info(f"🔍 RESULTADO DETECCIÓN para email {email_id_for_logging}: {has_zip_in_body}")
                            if self.config.get('log_zip_detection', False):
                                logger.info(f"Email {email_id_for_logging}: ZIP in bodystructure = {has_zip_in_body}")
                                if has_zip_in_body:
                                    logger.info(f"Email {email_id_for_logging}: Bodystructure snippet: {bodystructure[:200]}...")

                            if has_zip_in_body:
                                # Do a more detailed check to confirm
                                confirmed = self._confirm_zip_attachment(mail, email_id_str)
                                if self.config.get('log_zip_detection', False):
                                    logger.info(f"Email {email_id_for_logging}: ZIP confirmed = {confirmed}")

                                # Si no se confirma, probar método agresivo
                                if not confirmed:
                                    confirmed = self._aggressive_zip_detection(mail, email_id_str)
                                    if self.config.get('log_zip_detection', False):
                                        logger.info(f"Email {email_id_for_logging}: Aggressive ZIP detection = {confirmed}")

                                if confirmed:
                                    zip_email_ids.append(email_id)  # Keep original format
                                    logger.info(f"Email {email_id_for_logging} has ZIP attachment")
                        else:
                            if self.config.get('log_zip_detection', False):
                                logger.info(f"Email {email_id_for_logging}: No bodystructure data")

                except Exception as e:
                    logger.warning(f"Failed to check email {email_id_for_logging} for ZIP attachments: {str(e)}")
                    # Continue with next email rather than failing the entire batch
                    continue

            return zip_email_ids

        except Exception as e:
            logger.error(f"Failed to check batch for ZIP attachments: {str(e)}")
            raise

    def _has_zip_in_bodystructure(self, bodystructure: str) -> bool:
        """
        Check if email has ZIP attachment based on BODYSTRUCTURE with detailed logging
        """
        try:
            bodystructure_lower = bodystructure.lower()
            
            # SIEMPRE loggear - sin importar configuración
            logger.info(f"🔍 DEBUGGING - Full bodystructure: {bodystructure[:500]}...")
            logger.info(f"🔍 DEBUGGING - Bodystructure length: {len(bodystructure)}")
            
            zip_indicators = [
                'application/zip',
                'application/x-zip',
                'application/x-zip-compressed',
                'application/octet-stream',
                'application/x-compressed',
                'multipart/x-zip',
                'application/pkcs7-mime',
                'application/vnd.ms-office',
            ]
            
            zip_filename_patterns = [
                '.zip',
                'filename=',
                'name=',
                'attachment',
                'content-disposition',
                'factura',
                'invoice',
                'documento',
            ]
            
            # Check content type indicators
            found_content_indicators = []
            for indicator in zip_indicators:
                if indicator in bodystructure_lower:
                    found_content_indicators.append(indicator)
            
            # Check filename patterns
            found_filename_patterns = []
            for pattern in zip_filename_patterns:
                if pattern in bodystructure_lower:
                    found_filename_patterns.append(pattern)
            
            has_zip_content_type = len(found_content_indicators) > 0
            has_zip_filename = '.zip' in bodystructure_lower and len(found_filename_patterns) > 1
            
            # Detailed logging
            logger.info(f"DEBUGGING - Content indicators found: {found_content_indicators}")
            logger.info(f"DEBUGGING - Filename patterns found: {found_filename_patterns}")
            logger.info(f"DEBUGGING - Has ZIP content type: {has_zip_content_type}")
            logger.info(f"DEBUGGING - Has ZIP filename: {has_zip_filename}")
            
            result = has_zip_content_type or has_zip_filename
            logger.info(f"DEBUGGING - Final ZIP detection result: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in _has_zip_in_bodystructure: {str(e)}")
            return False