"""
Email connection and processing service
"""

import imaplib
import email
import os
import uuid
from datetime import datetime, timedelta
from typing import List, Tuple, Optional, Dict, Any
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
import ssl

from sqlalchemy.orm import Session
from shared.database.models import EmailRecord, ZipFileRecord
from shared.schemas.mailbox import EmailProcessRequest, DownloadedFile, EmailInfo
from shared.utils.logger import get_logger
from shared.utils.file_utils import ensure_directory, get_file_size, safe_filename
from shared.utils.date_utils import DateRangeHelper
from batch_config import BatchProcessingConfig

logger = get_logger(__name__)

class EmailService:
    """
    Service for connecting to email servers and processing emails
    """
    
    def __init__(self, temp_files_path: str = "./temp_files"):
        self.temp_files_path = temp_files_path
        ensure_directory(temp_files_path)

        # Load batch processing configuration
        self.config = BatchProcessingConfig.get_config()
        logger.info(f"Email service initialized with batch config: batch_size={self.config['batch_size']}, zip_filtering={self.config['enable_zip_filtering']}")

    def _validate_date_format(self, date_str: str) -> bool:
        """
        Validate IMAP date format (DD-MMM-YYYY)

        Args:
            date_str: Date string to validate

        Returns:
            True if valid, False otherwise
        """
        return DateRangeHelper.validate_imap_date_format(date_str)

    def _build_date_filter_criteria(self, date_filter: Optional[str]) -> List[str]:
        """
        Build IMAP date filter criteria from date_filter string with validation

        Args:
            date_filter: Date filter string (e.g., "SINCE 01-Jan-2024", "SINCE 01-Jan-2024 BEFORE 31-Jan-2024")

        Returns:
            List of IMAP search criteria

        Raises:
            ValueError: If date format is invalid
        """
        if not date_filter:
            return []

        # Split the date filter to handle multiple criteria
        criteria = []
        parts = date_filter.strip().split()

        i = 0
        while i < len(parts):
            if parts[i].upper() in ['SINCE', 'BEFORE', 'ON']:
                if i + 1 < len(parts):
                    date_part = parts[i + 1]
                    # Validate date format
                    if not self._validate_date_format(date_part):
                        raise ValueError(f"Invalid date format '{date_part}'. Expected format: DD-MMM-YYYY (e.g., 01-Jan-2024)")

                    criteria.append(f"{parts[i].upper()} {date_part}")
                    i += 2
                else:
                    raise ValueError(f"Missing date after '{parts[i]}' in date filter: {date_filter}")
            else:
                i += 1

        if not criteria:
            # If no recognized keywords, treat the entire string as a single criterion
            # But validate it first
            if not self._validate_date_format(date_filter):
                raise ValueError(f"Invalid date filter format: {date_filter}. Expected format: 'SINCE DD-MMM-YYYY' or 'BEFORE DD-MMM-YYYY'")
            criteria.append(date_filter)

        # Validate date range logic
        self._validate_date_range_logic(criteria)

        logger.info(f"Built date filter criteria: {criteria}")
        return criteria

    def _validate_date_range_logic(self, criteria: List[str]) -> None:
        """
        Validate that date range logic makes sense (SINCE date should be before BEFORE date)

        Args:
            criteria: List of date criteria

        Raises:
            ValueError: If date range logic is invalid
        """
        since_date = None
        before_date = None

        for criterion in criteria:
            parts = criterion.split()
            if len(parts) >= 2:
                command = parts[0].upper()
                date_str = parts[1]

                try:
                    date_obj = DateRangeHelper.parse_imap_date(date_str)

                    if command == 'SINCE':
                        since_date = date_obj
                    elif command == 'BEFORE':
                        before_date = date_obj
                except ValueError:
                    continue  # Skip invalid dates (already validated above)

        # Check if SINCE date is after BEFORE date
        if since_date and before_date and since_date >= before_date:
            raise ValueError(f"Invalid date range: SINCE date ({since_date.strftime('%d-%b-%Y')}) must be before BEFORE date ({before_date.strftime('%d-%b-%Y')})")

    @staticmethod
    def create_date_range_filter(start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> str:
        """
        Create an IMAP date filter string from datetime objects

        Args:
            start_date: Start date for filtering (inclusive)
            end_date: End date for filtering (exclusive)

        Returns:
            IMAP date filter string
        """
        return DateRangeHelper.create_imap_date_filter(start_date, end_date)
    
    def connect_to_mailbox(self, request: EmailProcessRequest) -> imaplib.IMAP4_SSL:
        """
        Connect to email server using IMAP
        
        Args:
            request: Email connection parameters
            
        Returns:
            IMAP connection object
            
        Raises:
            Exception: If connection fails
        """
        try:
            logger.info(f"Connecting to {request.email_host}:{request.email_port}")
            
            if request.use_ssl:
                # Create SSL context
                context = ssl.create_default_context()
                mail = imaplib.IMAP4_SSL(request.email_host, request.email_port, ssl_context=context)
            else:
                mail = imaplib.IMAP4(request.email_host, request.email_port)
            
            # Login
            mail.login(request.email_username, request.email_password)
            logger.info("Successfully connected to mailbox")
            
            return mail
            
        except Exception as e:
            logger.error(f"Failed to connect to mailbox: {str(e)}")
            raise Exception(f"Email connection failed: {str(e)}")
    
    def search_emails_with_attachments(
        self,
        mail: imaplib.IMAP4_SSL,
        request: EmailProcessRequest
    ) -> List[bytes]:
        """
        Search for emails with attachments

        Args:
            mail: IMAP connection
            request: Email processing request

        Returns:
            List of email IDs
        """
        try:
            # Select folder
            mail.select(request.folder)

            # Build search criteria - optimize for emails with attachments
            search_criteria = ['ALL']

            # Add date filtering criteria with validation
            try:
                date_criteria = self._build_date_filter_criteria(request.date_filter)
                search_criteria.extend(date_criteria)
            except ValueError as e:
                logger.error(f"Date filter validation error: {str(e)}")
                raise Exception(f"Invalid date filter: {str(e)}")

            # Log search criteria for debugging
            logger.info(f"IMAP search criteria: {search_criteria}")

            # Search for emails
            status, messages = mail.search(None, *search_criteria)

            if status != 'OK':
                raise Exception("Failed to search emails")

            email_ids = messages[0].split()

            # Debug logging to understand email ID types
            if email_ids:
                logger.debug(f"Email ID type: {type(email_ids[0])}, sample: {email_ids[0]}")

            # Enhanced logging with date filter information
            if request.date_filter:
                logger.info(f"Found {len(email_ids)} emails matching date filter '{request.date_filter}'")
            else:
                logger.info(f"Found {len(email_ids)} emails (no date filter applied)")

            return email_ids

        except Exception as e:
            logger.error(f"Failed to search emails: {str(e)}")
            raise

    def batch_filter_emails_with_zip_attachments(
        self,
        mail: imaplib.IMAP4_SSL,
        email_ids: List[bytes],
        batch_size: int = 50
    ) -> List[bytes]:
        """
        Efficiently filter emails to find only those with ZIP attachments using batch processing

        Args:
            mail: IMAP connection
            email_ids: List of all email IDs to check
            batch_size: Number of emails to process in each batch

        Returns:
            List of email IDs that have ZIP attachments
        """
        zip_email_ids = []
        total_emails = len(email_ids)

        logger.info(f"Batch filtering {total_emails} emails for ZIP attachments (batch size: {batch_size})")

        try:
            for i in range(0, total_emails, batch_size):
                batch = email_ids[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                total_batches = (total_emails + batch_size - 1) // batch_size

                logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} emails)")

                # Process batch to find ZIP attachments
                batch_zip_emails = self._check_batch_for_zip_attachments(mail, batch)
                zip_email_ids.extend(batch_zip_emails)

                logger.info(f"Batch {batch_num}: Found {len(batch_zip_emails)} emails with ZIP attachments")

            logger.info(f"Batch filtering complete: {len(zip_email_ids)}/{total_emails} emails have ZIP attachments")
            return zip_email_ids

        except Exception as e:
            logger.error(f"Failed to batch filter emails: {str(e)}")
            raise

    def _check_batch_for_zip_attachments(self, mail: imaplib.IMAP4_SSL, email_ids: List[bytes]) -> List[bytes]:
        """
        Check a batch of emails for ZIP attachments using efficient header-only fetching

        Args:
            mail: IMAP connection
            email_ids: Batch of email IDs to check

        Returns:
            List of email IDs that have ZIP attachments
        """
        zip_email_ids = []

        try:
            for email_id in email_ids:
                try:
                    # Convert email_id to string for IMAP fetch (handle both bytes and int)
                    if isinstance(email_id, bytes):
                        email_id_str = email_id.decode()
                        email_id_for_logging = email_id_str
                    elif isinstance(email_id, int):
                        email_id_str = str(email_id)
                        email_id_for_logging = email_id_str
                    else:
                        email_id_str = str(email_id)
                        email_id_for_logging = str(email_id)

                    # First, do a quick check using BODYSTRUCTURE (more efficient than full fetch)
                    status, msg_data = mail.fetch(email_id_str, '(BODYSTRUCTURE)')

                    # Always log the raw IMAP response for debugging
                    logger.info(f"Email {email_id_for_logging}: IMAP fetch status: {status}, msg_data type: {type(msg_data)}, length: {len(msg_data) if msg_data else 0}")

                    if status == 'OK' and msg_data and msg_data[0]:
                        # Handle both bytes and non-bytes data from IMAP response
                        bodystructure_data = msg_data[0][1]

                        # Always log bodystructure data for debugging
                        logger.info(f"Email {email_id_for_logging}: Raw bodystructure type: {type(bodystructure_data).__name__}, value: {str(bodystructure_data)[:200]}...")

                        if isinstance(bodystructure_data, bytes):
                            bodystructure = bodystructure_data.decode('utf-8', errors='ignore')
                        else:
                            # Handle case where IMAP returns non-bytes data (e.g., integer)
                            # This can happen with certain IMAP server responses
                            logger.info(f"Email {email_id_for_logging}: BODYSTRUCTURE returned {type(bodystructure_data).__name__} instead of bytes: {bodystructure_data}")
                            bodystructure = str(bodystructure_data)

                        # Quick check for ZIP files in body structure (only if we have valid string data)
                        if bodystructure:
                            has_zip_in_body = self._has_zip_in_bodystructure(bodystructure)
                            if self.config.get('log_zip_detection', False):
                                logger.info(f"Email {email_id_for_logging}: ZIP in bodystructure = {has_zip_in_body}")
                                if has_zip_in_body:
                                    logger.info(f"Email {email_id_for_logging}: Bodystructure snippet: {bodystructure[:200]}...")

                            if has_zip_in_body:
                                # Do a more detailed check to confirm
                                confirmed = self._confirm_zip_attachment(mail, email_id_str)
                                if self.config.get('log_zip_detection', False):
                                    logger.info(f"Email {email_id_for_logging}: ZIP confirmed = {confirmed}")

                                if confirmed:
                                    zip_email_ids.append(email_id)  # Keep original format
                                    logger.info(f"Email {email_id_for_logging} has ZIP attachment")
                        else:
                            if self.config.get('log_zip_detection', False):
                                logger.info(f"Email {email_id_for_logging}: No bodystructure data")

                except Exception as e:
                    logger.warning(f"Failed to check email {email_id_for_logging} for ZIP attachments: {str(e)}")
                    # Continue with next email rather than failing the entire batch
                    continue

            return zip_email_ids

        except Exception as e:
            logger.error(f"Failed to check batch for ZIP attachments: {str(e)}")
            raise

    def _has_zip_in_bodystructure(self, bodystructure: str) -> bool:
        """
        Quick check if bodystructure contains ZIP file indicators

        Args:
            bodystructure: Email bodystructure string

        Returns:
            True if likely contains ZIP attachment
        """
        # Look for common ZIP file indicators in bodystructure
        zip_indicators = [
            'application/zip',
            'application/x-zip',
            'application/x-zip-compressed',
            'application/octet-stream',  # ZIP files are sometimes sent as octet-stream
        ]

        # Also check for .zip extension in filename patterns
        zip_filename_patterns = [
            '.zip',
            'filename=',
            'name=',
        ]

        bodystructure_lower = bodystructure.lower()

        # Check for ZIP content types
        has_zip_content_type = any(indicator in bodystructure_lower for indicator in zip_indicators)

        # Check for .zip in filename (more specific check)
        has_zip_filename = '.zip' in bodystructure_lower and any(pattern in bodystructure_lower for pattern in zip_filename_patterns)

        has_zip = has_zip_content_type or has_zip_filename

        # Log detailed info if ZIP detection logging is enabled
        if self.config.get('log_zip_detection', False):
            if has_zip:
                found_content_types = [indicator for indicator in zip_indicators if indicator in bodystructure_lower]
                found_filename_patterns = [pattern for pattern in zip_filename_patterns if pattern in bodystructure_lower]
                logger.info(f"ZIP found - Content types: {found_content_types}, Filename patterns: {found_filename_patterns}")
            else:
                # Log a sample of the bodystructure for debugging
                logger.info(f"No ZIP detected. Bodystructure sample: {bodystructure_lower[:200]}...")

        return has_zip

    def _confirm_zip_attachment(self, mail: imaplib.IMAP4_SSL, email_id: str) -> bool:
        """
        Confirm that an email actually has ZIP attachments by checking headers

        Args:
            mail: IMAP connection
            email_id: Email ID to check (as string)

        Returns:
            True if email has ZIP attachments
        """
        try:
            # Fetch only headers and structure (more efficient than full message)
            status, msg_data = mail.fetch(email_id, '(BODY.PEEK[HEADER] BODYSTRUCTURE)')

            if status != 'OK' or not msg_data:
                return False

            # Parse the message headers
            header_data = msg_data[0][1]
            if isinstance(header_data, bytes):
                header_data = header_data.decode('utf-8', errors='ignore')

            # Look for attachment indicators in headers
            header_lower = header_data.lower()

            # Check for Content-Disposition: attachment with .zip files
            if 'content-disposition:' in header_lower and 'attachment' in header_lower:
                if '.zip' in header_lower:
                    return True

            # Check for Content-Type: application/zip
            if 'content-type:' in header_lower:
                if any(zip_type in header_lower for zip_type in ['application/zip', 'application/x-zip']):
                    return True

            return False

        except Exception as e:
            logger.warning(f"Failed to confirm ZIP attachment for email {email_id}: {str(e)}")
            return False
    
    def get_email_info(self, mail: imaplib.IMAP4_SSL, email_id: bytes) -> Dict[str, Any]:
        """
        Get email information and check for ZIP attachments
        
        Args:
            mail: IMAP connection
            email_id: Email ID
            
        Returns:
            Dictionary with email information
        """
        try:
            # Fetch email
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            
            if status != 'OK':
                raise Exception(f"Failed to fetch email {email_id}")
            
            # Parse email
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # Extract basic info
            subject = email_message.get('Subject', '')
            sender = email_message.get('From', '')
            date_str = email_message.get('Date', '')
            
            # Parse date
            reception_date = datetime.now()
            if date_str:
                try:
                    reception_date = email.utils.parsedate_to_datetime(date_str)
                except:
                    pass
            
            # Check for ZIP attachments
            zip_attachments = []
            attachment_count = 0
            
            for part in email_message.walk():
                if part.get_content_disposition() == 'attachment':
                    attachment_count += 1
                    filename = part.get_filename()
                    if filename and filename.lower().endswith('.zip'):
                        zip_attachments.append({
                            'filename': filename,
                            'part': part
                        })
            
            # Convert email_id to string safely
            email_id_str = email_id.decode() if isinstance(email_id, bytes) else str(email_id)

            return {
                'email_id': email_id_str,
                'subject': subject,
                'sender': sender,
                'reception_date': reception_date,
                'has_zip_attachment': len(zip_attachments) > 0,
                'attachment_count': attachment_count,
                'zip_attachments': zip_attachments,
                'email_message': email_message
            }
            
        except Exception as e:
            logger.error(f"Failed to get email info for {email_id}: {str(e)}")
            raise
    
    def download_zip_attachments(
        self, 
        email_info: Dict[str, Any], 
        db: Session
    ) -> List[DownloadedFile]:
        """
        Download ZIP attachments from email
        
        Args:
            email_info: Email information dictionary
            db: Database session
            
        Returns:
            List of downloaded files
        """
        downloaded_files = []
        
        try:
            for attachment in email_info['zip_attachments']:
                filename = safe_filename(attachment['filename'])
                
                # Generate unique filename
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(self.temp_files_path, unique_filename)
                
                # Download attachment
                with open(file_path, 'wb') as f:
                    f.write(attachment['part'].get_payload(decode=True))
                
                file_size = get_file_size(file_path)
                download_date = datetime.now()
                
                # Create downloaded file info
                downloaded_file = DownloadedFile(
                    filename=attachment['filename'],
                    file_path=file_path,
                    file_size=file_size,
                    email_id=email_info['email_id'],
                    download_date=download_date
                )
                
                downloaded_files.append(downloaded_file)
                
                logger.info(f"Downloaded ZIP attachment: {filename} ({file_size} bytes)")
            
            return downloaded_files
            
        except Exception as e:
            logger.error(f"Failed to download ZIP attachments: {str(e)}")
            raise
    
    def store_email_record(self, email_info: Dict[str, Any], db: Session, client_id: Optional[int] = None) -> EmailRecord:
        """
        Store email record in database

        Args:
            email_info: Email information
            db: Database session
            client_id: Client ID to associate with the email record

        Returns:
            Created EmailRecord
        """
        try:
            # Validate client_id is provided
            if client_id is None:
                logger.warning(f"No client_id provided for email {email_info['email_id']}, email will not be associated with any client")

            # Check if email already exists
            existing_email = db.query(EmailRecord).filter(
                EmailRecord.email_id == email_info['email_id']
            ).first()

            if existing_email:
                logger.info(f"Email {email_info['email_id']} already exists in database")

                # Update client_id if it's missing or different
                if existing_email.client_id != client_id and client_id is not None:
                    logger.info(f"Updating client_id for existing email {email_info['email_id']} from {existing_email.client_id} to {client_id}")
                    existing_email.client_id = client_id
                    db.commit()
                    db.refresh(existing_email)

                return existing_email

            # Create new email record
            email_record = EmailRecord(
                email_id=email_info['email_id'],
                sender=email_info['sender'],
                subject=email_info['subject'],
                reception_date=email_info['reception_date'],
                has_zip_attachment=email_info['has_zip_attachment'],
                processing_status="processing",
                client_id=client_id
            )
            
            db.add(email_record)
            db.commit()
            db.refresh(email_record)

            logger.info(f"Stored email record: {email_info['email_id']} with client_id: {client_id}")
            return email_record
            
        except Exception as e:
            logger.error(f"Failed to store email record: {str(e)}")
            db.rollback()
            raise
    
    def store_zip_file_records(
        self, 
        downloaded_files: List[DownloadedFile], 
        email_record: EmailRecord, 
        db: Session
    ) -> List[ZipFileRecord]:
        """
        Store ZIP file records in database
        
        Args:
            downloaded_files: List of downloaded files
            email_record: Associated email record
            db: Database session
            
        Returns:
            List of created ZipFileRecord objects
        """
        zip_records = []
        
        try:
            for downloaded_file in downloaded_files:
                zip_record = ZipFileRecord(
                    email_record_id=email_record.id,
                    original_filename=downloaded_file.filename,
                    file_path=downloaded_file.file_path,
                    file_size=downloaded_file.file_size,
                    download_date=downloaded_file.download_date,
                    extraction_status="pending"
                )
                
                db.add(zip_record)
                zip_records.append(zip_record)
            
            db.commit()
            
            for record in zip_records:
                db.refresh(record)
            
            logger.info(f"Stored {len(zip_records)} ZIP file records")
            return zip_records
            
        except Exception as e:
            logger.error(f"Failed to store ZIP file records: {str(e)}")
            db.rollback()
            raise
    
    def process_emails(self, request: EmailProcessRequest, db: Session) -> Tuple[int, List[DownloadedFile], List[str], int, List[str], List[str]]:
        """
        Main method to process emails with optimized batch processing for ZIP attachments

        Args:
            request: Email processing request
            db: Database session

        Returns:
            Tuple of (processed_count, downloaded_files, errors, emails_with_zip_attachments, sample_senders, sample_subjects)
        """
        mail = None
        processed_count = 0
        downloaded_files = []
        errors = []
        emails_with_zip_attachments = 0
        sample_senders = []
        sample_subjects = []

        try:
            # Log processing start with date filter information
            if request.date_filter:
                logger.info(f"Starting optimized email processing with date filter: '{request.date_filter}'")
            else:
                logger.info("Starting optimized email processing without date filter (processing all emails)")

            # Connect to mailbox
            mail = self.connect_to_mailbox(request)

            # Search for all emails first
            all_email_ids = self.search_emails_with_attachments(mail, request)

            if not all_email_ids:
                logger.info("No emails found matching criteria")
                return processed_count, downloaded_files, errors, emails_with_zip_attachments, sample_senders, sample_subjects

            # Batch filter to find only emails with ZIP attachments
            logger.info("Filtering emails for ZIP attachments...")
            zip_email_ids = self.batch_filter_emails_with_zip_attachments(
                mail, all_email_ids, batch_size=self.config['zip_filter_batch_size']
            )

            if not zip_email_ids:
                logger.info("No emails with ZIP attachments found")
                # Still process non-ZIP emails for record keeping
                max_non_zip = self.config['max_non_zip_emails']
                if max_non_zip > 0:
                    logger.info(f"Processing up to {max_non_zip} non-ZIP emails for record keeping...")
                    non_zip_processed = self._process_non_zip_emails(mail, all_email_ids[:max_non_zip], db, request.client_id)
                    # Collect sample information from processed emails
                    for email_id in all_email_ids[:min(5, len(all_email_ids))]:
                        try:
                            email_info = self._get_basic_email_info(mail, email_id)
                            sample_senders.append(email_info['sender'])
                            sample_subjects.append(email_info['subject'])
                        except:
                            pass
                else:
                    logger.info("Skipping non-ZIP emails (max_non_zip_emails=0)")
                    non_zip_processed = 0
                return non_zip_processed, downloaded_files, errors, emails_with_zip_attachments, sample_senders, sample_subjects

            logger.info(f"Found {len(zip_email_ids)} emails with ZIP attachments. Processing...")

            # Process only emails with ZIP attachments
            for email_id in zip_email_ids:
                try:
                    # Get email information
                    email_info = self.get_email_info(mail, email_id)

                    # Store email record
                    email_record = self.store_email_record(email_info, db, request.client_id)

                    # Collect sample information
                    if len(sample_senders) < 5:
                        sample_senders.append(email_info['sender'])
                    if len(sample_subjects) < 5:
                        sample_subjects.append(email_info['subject'])

                    # Download ZIP attachments (we know they exist)
                    if email_info['has_zip_attachment']:
                        emails_with_zip_attachments += 1
                        email_downloaded_files = self.download_zip_attachments(email_info, db)
                        downloaded_files.extend(email_downloaded_files)

                        # Store ZIP file records
                        self.store_zip_file_records(email_downloaded_files, email_record, db)

                        # Convert email_id to string safely for logging
                        email_id_str = email_id.decode() if isinstance(email_id, bytes) else str(email_id)
                        logger.info(f"Successfully processed email {email_id_str} with {len(email_downloaded_files)} ZIP files")

                    # Update email record status
                    email_record.processing_status = "completed"
                    db.commit()

                    processed_count += 1

                except Exception as e:
                    error_msg = f"Failed to process email {email_id}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)

                    # Update email record with error
                    try:
                        # Convert email_id to string safely for database query
                        email_id_str = email_id.decode() if isinstance(email_id, bytes) else str(email_id)
                        email_record = db.query(EmailRecord).filter(
                            EmailRecord.email_id == email_id_str
                        ).first()
                        if email_record:
                            email_record.processing_status = "failed"
                            email_record.error_message = str(e)
                            db.commit()
                    except:
                        pass

            # Process a sample of non-ZIP emails for record keeping (optional)
            remaining_emails = [eid for eid in all_email_ids if eid not in zip_email_ids]
            max_non_zip = self.config['max_non_zip_emails']
            if remaining_emails and max_non_zip > 0:
                sample_size = min(max_non_zip, len(remaining_emails))
                logger.info(f"Processing sample of {sample_size} non-ZIP emails for record keeping...")
                non_zip_processed = self._process_non_zip_emails(mail, remaining_emails[:sample_size], db, request.client_id)
                processed_count += non_zip_processed
            elif max_non_zip == 0:
                logger.info("Skipping non-ZIP emails (max_non_zip_emails=0)")

            logger.info(f"Email processing complete: {processed_count} total emails processed, {len(downloaded_files)} ZIP files downloaded")
            return processed_count, downloaded_files, errors, emails_with_zip_attachments, sample_senders, sample_subjects

        except Exception as e:
            error_msg = f"Email processing failed: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            return processed_count, downloaded_files, errors, emails_with_zip_attachments, sample_senders, sample_subjects

        finally:
            if mail:
                try:
                    mail.close()
                    mail.logout()
                except:
                    pass

    def _process_non_zip_emails(self, mail: imaplib.IMAP4_SSL, email_ids: List[bytes], db: Session, client_id: Optional[int]) -> int:
        """
        Process non-ZIP emails for record keeping (lightweight processing)

        Args:
            mail: IMAP connection
            email_ids: List of email IDs to process
            db: Database session
            client_id: Client ID

        Returns:
            Number of emails processed
        """
        processed_count = 0

        for email_id in email_ids:
            try:
                # Get basic email information (lightweight)
                email_info = self._get_basic_email_info(mail, email_id)

                # Store email record
                email_record = self.store_email_record(email_info, db, client_id)

                # Update email record status
                email_record.processing_status = "completed"
                db.commit()

                processed_count += 1

            except Exception as e:
                # Convert email_id to string safely for logging
                email_id_str = email_id.decode() if isinstance(email_id, bytes) else str(email_id)
                logger.warning(f"Failed to process non-ZIP email {email_id_str}: {str(e)}")
                continue

        return processed_count

    def _get_basic_email_info(self, mail: imaplib.IMAP4_SSL, email_id: bytes) -> Dict[str, Any]:
        """
        Get basic email information without checking attachments (lightweight)

        Args:
            mail: IMAP connection
            email_id: Email ID

        Returns:
            Dictionary with basic email information
        """
        try:
            # Fetch only headers for efficiency
            status, msg_data = mail.fetch(email_id, '(BODY.PEEK[HEADER])')

            if status != 'OK':
                raise Exception(f"Failed to fetch email headers {email_id}")

            # Parse email headers
            header_data = msg_data[0][1]
            if isinstance(header_data, bytes):
                header_data = header_data.decode('utf-8', errors='ignore')

            # Extract basic info from headers
            lines = header_data.split('\n')
            subject = ""
            sender = ""
            date_str = ""

            for line in lines:
                line = line.strip()
                if line.lower().startswith('subject:'):
                    subject = line[8:].strip()
                elif line.lower().startswith('from:'):
                    sender = line[5:].strip()
                elif line.lower().startswith('date:'):
                    date_str = line[5:].strip()

            # Parse date
            reception_date = datetime.now()
            if date_str:
                try:
                    reception_date = email.utils.parsedate_to_datetime(date_str)
                except:
                    pass

            # Convert email_id to string safely
            email_id_str = email_id.decode() if isinstance(email_id, bytes) else str(email_id)

            return {
                'email_id': email_id_str,
                'subject': subject,
                'sender': sender,
                'reception_date': reception_date,
                'has_zip_attachment': False,  # We know these don't have ZIP attachments
                'attachment_count': 0,
                'zip_attachments': [],
                'email_message': None  # Not needed for basic processing
            }

        except Exception as e:
            logger.error(f"Failed to get basic email info for {email_id}: {str(e)}")
            raise
