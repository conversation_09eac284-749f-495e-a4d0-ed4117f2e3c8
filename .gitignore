# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv
venv/
ENV/
env/
.env/

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/

# Node.js / React Frontend
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Frontend build outputs
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/

# Logs
logs
*.log

# Docker
.dockerignore

# Database
*.db
*.sqlite
*.sqlite3

# File uploads and downloads
uploads/
downloads/
temp/
tmp/

# ZIP files and archives
*.zip
*.tar.gz
*.rar
*.7z

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Email attachments and processed files
email_attachments/
processed_files/
extracted_files/

# Backup files
*.bak
*.backup
*.old

# IDE files
*.swp
*.swo
*~

# Certificate files
*.pem
*.key
*.crt

# Local configuration overrides
config/local.py
local_settings.py

# Office documents and spreadsheets
*.xlsx
*.xls
*.xlsm
*.xlsb
*.xltx
*.xltm
*.csv
*.ods
*.doc
*.docx
*.docm
*.dot
*.dotx
*.dotm
*.odt
*.rtf
*.ppt
*.pptx
*.pptm
*.pot
*.potx
*.potm
*.pps
*.ppsx
*.ppsm
*.odp

# XML and data files (except configuration)
*.xml
!*config*.xml
!*settings*.xml
!*web*.xml
!*pom*.xml
!*build*.xml
!*ant*.xml
!*maven*.xml
!*gradle*.xml
!*package*.xml
*.xsd
*.xsl
*.xslt
*.dtd

# PDF files
*.pdf

# Image files (except essential assets)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.tif
*.ico
*.svg
*.webp
*.avif
!frontend/public/**/*.png
!frontend/public/**/*.jpg
!frontend/public/**/*.jpeg
!frontend/public/**/*.gif
!frontend/public/**/*.svg
!frontend/public/**/*.ico
!frontend/src/assets/**/*.png
!frontend/src/assets/**/*.jpg
!frontend/src/assets/**/*.jpeg
!frontend/src/assets/**/*.gif
!frontend/src/assets/**/*.svg
!frontend/src/assets/**/*.ico

# Audio and video files
*.mp3
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mkv
*.wav
*.flac
*.aac
*.ogg
*.m4a

# Text and documentation files (except README and essential docs)
*.txt
!README*.txt
!LICENSE*.txt
!CHANGELOG*.txt
!requirements*.txt
!package*.txt
*.md
!README*.md
!LICENSE*.md
!CHANGELOG*.md
!CONTRIBUTING*.md
!CODE_OF_CONDUCT*.md
!SECURITY*.md
!docs/**/*.md

# Sample and test data files
sample_*
test_data/
mock_data/
fixtures/
seeds/
examples/
demo_*

# Temporary and cache files
*.tmp
*.temp
*.cache
.cache/
.temp/
.tmp/

# Email files and attachments
*.eml
*.msg
*.mbox
*.pst
*.ost

# Archive and compressed files (additional)
*.gz
*.bz2
*.xz
*.lz
*.lzma
*.Z
*.deb
*.rpm
*.dmg
*.pkg
*.msi
*.exe
*.app

# Database dumps and exports
*.sql
*.dump
*.backup
*.bak
dump.rdb
*.rdb

# Configuration files with sensitive data
secrets.json
credentials.json
service-account*.json
*.p12
*.pfx
*.jks
*.keystore

# IDE and editor specific files
.sublime-*
*.sublime-project
*.sublime-workspace
.atom/
.brackets.json
*.code-workspace

# System and hidden files
.fuse_hidden*
.directory
.Trash-*
.nfs*
*.lnk
desktop.ini
$RECYCLE.BIN/

# Application specific data directories
data/
storage/
files/
media/
static_files/
user_uploads/
attachments/
documents/
