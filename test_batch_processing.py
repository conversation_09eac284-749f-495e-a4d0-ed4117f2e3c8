#!/usr/bin/env python3
"""
Script para probar específicamente el procesamiento por batches
"""

import requests
import json
import time

def test_batch_processing():
    """Probar el procesamiento por batches con logging forzado"""
    
    print("🧪 PROBANDO PROCESAMIENTO POR BATCHES...")
    
    # Request mínimo para activar el procesamiento
    test_request = {
        "email_host": "test.example.com",
        "email_port": 993,
        "email_username": "<EMAIL>",
        "email_password": "test_password",
        "use_ssl": True,
        "folder": "INBOX",
        "date_from": "2025-09-16",
        "date_to": "2025-09-17",
        "max_emails": 5,
        "client_id": 1
    }
    
    try:
        # Usar el endpoint de batch processing
        response = requests.post(
            'http://localhost:8001/process-emails-batch?batch_size=2&zip_only=true',
            json=test_request,
            timeout=60
        )
        
        print(f"📊 Respuesta del batch processing: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Batch processing respondió:")
            print(f"   {json.dumps(result, indent=2)}")
        else:
            print(f"⚠️  Error esperado (credenciales de prueba): {response.status_code}")
            print(f"   Respuesta: {response.text[:500]}...")
            
        print(f"\n💡 REVISAR LOGS:")
        print("   Buscar líneas que empiecen con '🔍' o '🎯'")
        print("   Estas líneas mostrarán si el batch processing se está ejecutando")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_batch_processing()
