#!/usr/bin/env python3
"""
Fix definitivo para el problema de detección ZIP en procesamiento por batches
"""

import os
import sys
from pathlib import Path

def fix_batch_processing_flow():
    """Corregir el flujo de procesamiento por batches"""
    print("=== CORRIGIENDO FLUJO DE PROCESAMIENTO POR BATCHES ===")
    
    email_service_path = Path('services/mailbox_service/email_service.py')
    
    if not email_service_path.exists():
        print("❌ No se encuentra email_service.py")
        return False
    
    # Leer el archivo
    with open(email_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. Forzar logging en batch_filter_emails_with_zip_attachments
    old_batch_start = """    def batch_filter_emails_with_zip_attachments(
        self,
        mail: imaplib.IMAP4_SSL,
        email_ids: List[bytes],
        batch_size: int = 50
    ) -> List[bytes]:
        """
        Efficiently filter emails to find only those with ZIP attachments using batch processing

        Args:
            mail: IMAP connection
            email_ids: List of all email IDs to check
            batch_size: Number of emails to process in each batch

        Returns:
            List of email IDs that have ZIP attachments
        """
        zip_email_ids = []
        total_emails = len(email_ids)

        logger.info(f"Batch filtering {total_emails} emails for ZIP attachments (batch size: {batch_size})")"""
    
    new_batch_start = """    def batch_filter_emails_with_zip_attachments(
        self,
        mail: imaplib.IMAP4_SSL,
        email_ids: List[bytes],
        batch_size: int = 50
    ) -> List[bytes]:
        """
        Efficiently filter emails to find only those with ZIP attachments using batch processing

        Args:
            mail: IMAP connection
            email_ids: List of all email IDs to check
            batch_size: Number of emails to process in each batch

        Returns:
            List of email IDs that have ZIP attachments
        """
        zip_email_ids = []
        total_emails = len(email_ids)

        # FORZAR logging detallado
        logger.info(f"🔍 BATCH FILTERING INICIADO: {total_emails} emails para revisar ZIP (batch size: {batch_size})")
        logger.info(f"🔍 CONFIGURACIÓN LOG_ZIP_DETECTION: {self.config.get('log_zip_detection', False)}")"""
    
    if old_batch_start in content:
        content = content.replace(old_batch_start, new_batch_start)
        print("✅ Logging forzado en batch_filter_emails_with_zip_attachments")
    
    # 2. Agregar logging detallado en _check_batch_for_zip_attachments
    old_check_start = """        try:
            for email_id in email_ids:
                try:
                    # Convert email_id to string for IMAP fetch (handle both bytes and int)"""
    
    new_check_start = """        try:
            logger.info(f"🔍 REVISANDO BATCH: {len(email_ids)} emails para detección ZIP")
            
            for email_id in email_ids:
                try:
                    # Convert email_id to string for IMAP fetch (handle both bytes and int)"""
    
    if old_check_start in content:
        content = content.replace(old_check_start, new_check_start)
        print("✅ Logging agregado en _check_batch_for_zip_attachments")
    
    # 3. Forzar que siempre se ejecute la detección
    old_bodystructure_check = """                        # Quick check for ZIP files in body structure (only if we have valid string data)
                        if bodystructure:
                            has_zip_in_body = self._has_zip_in_bodystructure(bodystructure)"""
    
    new_bodystructure_check = """                        # Quick check for ZIP files in body structure (only if we have valid string data)
                        if bodystructure:
                            logger.info(f"🔍 EJECUTANDO DETECCIÓN ZIP para email {email_id_for_logging}")
                            has_zip_in_body = self._has_zip_in_bodystructure(bodystructure)
                            logger.info(f"🔍 RESULTADO DETECCIÓN para email {email_id_for_logging}: {has_zip_in_body}")"""
    
    if old_bodystructure_check in content:
        content = content.replace(old_bodystructure_check, new_bodystructure_check)
        print("✅ Detección forzada agregada")
    
    # 4. Agregar fallback si no hay bodystructure
    old_no_bodystructure = """                        else:
                            if self.config.get('log_zip_detection', False):
                                logger.info(f"Email {email_id_for_logging}: No bodystructure data")"""
    
    new_no_bodystructure = """                        else:
                            logger.info(f"⚠️  Email {email_id_for_logging}: No bodystructure data - INTENTANDO MÉTODO ALTERNATIVO")
                            # Si no hay bodystructure, intentar método agresivo directamente
                            try:
                                confirmed = self._aggressive_zip_detection(mail, email_id_str)
                                logger.info(f"🔍 MÉTODO ALTERNATIVO para email {email_id_for_logging}: {confirmed}")
                                if confirmed:
                                    zip_email_ids.append(email_id)
                                    logger.info(f"✅ Email {email_id_for_logging} detectado como ZIP por método alternativo")
                            except Exception as e:
                                logger.error(f"❌ Error en método alternativo para email {email_id_for_logging}: {e}")"""
    
    if old_no_bodystructure in content:
        content = content.replace(old_no_bodystructure, new_no_bodystructure)
        print("✅ Método alternativo agregado para emails sin bodystructure")
    
    # Escribir el archivo modificado
    with open(email_service_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def force_zip_detection_always():
    """Forzar que la detección de ZIP siempre se ejecute"""
    print("\n=== FORZANDO DETECCIÓN ZIP SIEMPRE ===")
    
    email_service_path = Path('services/mailbox_service/email_service.py')
    
    # Leer el archivo
    with open(email_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Modificar _has_zip_in_bodystructure para que siempre loggee
    old_debug_start = """    def _has_zip_in_bodystructure(self, bodystructure: str) -> bool:
        """
        Check if email has ZIP attachment based on BODYSTRUCTURE with detailed logging
        """
        try:
            bodystructure_lower = bodystructure.lower()
            
            # Log the full bodystructure for debugging
            logger.info(f"DEBUGGING - Full bodystructure: {bodystructure[:500]}...")"""
    
    new_debug_start = """    def _has_zip_in_bodystructure(self, bodystructure: str) -> bool:
        """
        Check if email has ZIP attachment based on BODYSTRUCTURE with detailed logging
        """
        try:
            bodystructure_lower = bodystructure.lower()
            
            # SIEMPRE loggear - sin importar configuración
            logger.info(f"🔍 DEBUGGING - Full bodystructure: {bodystructure[:500]}...")
            logger.info(f"🔍 DEBUGGING - Bodystructure length: {len(bodystructure)}")"""
    
    if old_debug_start in content:
        content = content.replace(old_debug_start, new_debug_start)
        print("✅ Logging forzado en _has_zip_in_bodystructure")
    
    # Forzar que siempre loggee los resultados
    old_debug_results = """            # Detailed logging
            logger.info(f"DEBUGGING - Content indicators found: {found_content_indicators}")
            logger.info(f"DEBUGGING - Filename patterns found: {found_filename_patterns}")
            logger.info(f"DEBUGGING - Has ZIP content type: {has_zip_content_type}")
            logger.info(f"DEBUGGING - Has ZIP filename: {has_zip_filename}")
            
            result = has_zip_content_type or has_zip_filename
            logger.info(f"DEBUGGING - Final ZIP detection result: {result}")"""
    
    new_debug_results = """            # SIEMPRE loggear resultados detallados
            logger.info(f"🔍 DEBUGGING - Content indicators found: {found_content_indicators}")
            logger.info(f"🔍 DEBUGGING - Filename patterns found: {found_filename_patterns}")
            logger.info(f"🔍 DEBUGGING - Has ZIP content type: {has_zip_content_type}")
            logger.info(f"🔍 DEBUGGING - Has ZIP filename: {has_zip_filename}")
            
            result = has_zip_content_type or has_zip_filename
            logger.info(f"🎯 DEBUGGING - Final ZIP detection result: {result}")
            
            # Log adicional para diagnóstico
            if not result:
                logger.info(f"❌ DEBUGGING - ZIP NO DETECTADO. Bodystructure sample: {bodystructure_lower[:200]}...")"""
    
    if old_debug_results in content:
        content = content.replace(old_debug_results, new_debug_results)
        print("✅ Logging de resultados forzado")
    
    # Escribir el archivo modificado
    with open(email_service_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def create_batch_test_script():
    """Crear script para probar el procesamiento por batches"""
    print("\n=== CREANDO SCRIPT DE PRUEBA DE BATCHES ===")
    
    test_script = '''#!/usr/bin/env python3
"""
Script para probar específicamente el procesamiento por batches
"""

import requests
import json
import time

def test_batch_processing():
    """Probar el procesamiento por batches con logging forzado"""
    
    print("🧪 PROBANDO PROCESAMIENTO POR BATCHES...")
    
    # Request mínimo para activar el procesamiento
    test_request = {
        "email_host": "test.example.com",
        "email_port": 993,
        "email_username": "<EMAIL>",
        "email_password": "test_password",
        "use_ssl": True,
        "folder": "INBOX",
        "date_from": "2025-09-16",
        "date_to": "2025-09-17",
        "max_emails": 5,
        "client_id": 1
    }
    
    try:
        # Usar el endpoint de batch processing
        response = requests.post(
            'http://localhost:8001/process-emails-batch?batch_size=2&zip_only=true',
            json=test_request,
            timeout=60
        )
        
        print(f"📊 Respuesta del batch processing: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Batch processing respondió:")
            print(f"   {json.dumps(result, indent=2)}")
        else:
            print(f"⚠️  Error esperado (credenciales de prueba): {response.status_code}")
            print(f"   Respuesta: {response.text[:500]}...")
            
        print(f"\\n💡 REVISAR LOGS:")
        print("   Buscar líneas que empiecen con '🔍' o '🎯'")
        print("   Estas líneas mostrarán si el batch processing se está ejecutando")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_batch_processing()
'''
    
    with open('test_batch_processing.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ Script de prueba creado: test_batch_processing.py")

def main():
    """Función principal"""
    print("=" * 60)
    print("FIX DEFINITIVO - DETECCIÓN ZIP EN PROCESAMIENTO POR BATCHES")
    print("=" * 60)
    
    print("🎯 PROBLEMA IDENTIFICADO:")
    print("   Los logs no muestran 'DEBUGGING -' ni 'Batch filtering'")
    print("   Esto indica que el procesamiento por batches no ejecuta la detección ZIP")
    
    # 1. Corregir flujo de procesamiento por batches
    batch_fixed = fix_batch_processing_flow()
    
    # 2. Forzar detección ZIP siempre
    detection_forced = force_zip_detection_always()
    
    # 3. Crear script de prueba
    create_batch_test_script()
    
    print(f"\n{'='*60}")
    print("CORRECCIONES APLICADAS")
    print(f"{'='*60}")
    
    if batch_fixed and detection_forced:
        print("\n✅ TODAS LAS CORRECCIONES APLICADAS")
        
        print(f"\n🔧 PRÓXIMOS PASOS:")
        print("1. Reiniciar el mailbox service:")
        print("   - Detener: Ctrl+C en start_services_fixed.py")
        print("   - Reiniciar: python start_services_fixed.py")
        print("")
        print("2. Probar el batch processing:")
        print("   python test_batch_processing.py")
        print("")
        print("3. Usar el frontend para procesar emails")
        print("   - Los logs ahora mostrarán líneas con '🔍' y '🎯'")
        print("   - Esto confirmará que la detección se está ejecutando")
        
        print(f"\n💡 QUÉ ESPERAR EN LOS LOGS:")
        print("   🔍 BATCH FILTERING INICIADO: X emails para revisar ZIP")
        print("   🔍 REVISANDO BATCH: X emails para detección ZIP")
        print("   🔍 EJECUTANDO DETECCIÓN ZIP para email XXXXX")
        print("   🔍 DEBUGGING - Full bodystructure: ...")
        print("   🎯 DEBUGGING - Final ZIP detection result: True/False")
        
        print(f"\n🎉 RESULTADO ESPERADO:")
        print("   Una vez reiniciado el servicio, verás estos logs")
        print("   Y podrás identificar exactamente por qué no se detectan los ZIP")
    else:
        print("\n❌ HUBO PROBLEMAS EN LAS CORRECCIONES")
        print("   Revisar manualmente el archivo email_service.py")

if __name__ == "__main__":
    main()
