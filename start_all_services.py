#!/usr/bin/env python3
"""
Script para iniciar todos los servicios del sistema
"""

import subprocess
import sys
import time
import os

def start_service(service_name, service_path, port):
    """Iniciar un servicio específico"""
    print(f"Iniciando {service_name} en puerto {port}...")
    
    # Cambiar al directorio del servicio
    os.chdir(service_path)
    
    # Iniciar el servicio
    cmd = [
        sys.executable, "-m", "uvicorn", "main:app",
        "--host", "0.0.0.0",
        "--port", str(port),
        "--reload"
    ]
    
    process = subprocess.Popen(cmd)
    
    # Volver al directorio raíz
    os.chdir("../..")
    
    return process

def main():
    """Iniciar todos los servicios"""
    services = [
        ("API Service", "services/api_service", 8000),
        ("Mailbox Service", "services/mailbox_service", 8001),
        ("File Processing Service", "services/file_processing_service", 8002),
        ("Extraction Service", "services/extraction_service", 8003)
    ]
    
    processes = []
    
    for service_name, service_path, port in services:
        try:
            process = start_service(service_name, service_path, port)
            processes.append((service_name, process))
            time.sleep(2)  # Esperar un poco entre servicios
        except Exception as e:
            print(f"Error iniciando {service_name}: {e}")
    
    print("\nTodos los servicios iniciados. Presiona Ctrl+C para detener.")
    
    try:
        # Esperar indefinidamente
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nDeteniendo servicios...")
        for service_name, process in processes:
            print(f"Deteniendo {service_name}...")
            process.terminate()

if __name__ == "__main__":
    main()
