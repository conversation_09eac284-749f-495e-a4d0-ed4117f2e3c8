#!/usr/bin/env python3
"""
Script para crear archivos ZIP de prueba con facturas XML
"""

import os
import zipfile
from pathlib import Path

def create_test_xml_content(cufe_value, issuer_name="Test Company S.A.S.", document_number="FV-001"):
    """Crear contenido XML de prueba con CUFE"""
    
    xml_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2">
    
    <cbc:UBLVersionID>UBL 2.1</cbc:UBLVersionID>
    <cbc:CustomizationID>DIAN 2.1</cbc:CustomizationID>
    <cbc:ProfileID>DIAN 2.1: Factura Electrónica de Venta</cbc:ProfileID>
    <cbc:ID>{document_number}</cbc:ID>
    <cbc:UUID schemeName="CUFE">{cufe_value}</cbc:UUID>
    <cbc:IssueDate>2025-09-16</cbc:IssueDate>
    <cbc:IssueTime>10:30:00-05:00</cbc:IssueTime>
    <cbc:InvoiceTypeCode>01</cbc:InvoiceTypeCode>
    <cbc:DocumentCurrencyCode>COP</cbc:DocumentCurrencyCode>
    
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cac:PartyName>
                <cbc:Name>{issuer_name}</cbc:Name>
            </cac:PartyName>
            <cac:PartyTaxScheme>
                <cbc:RegistrationName>{issuer_name}</cbc:RegistrationName>
                <cbc:CompanyID schemeID="31">*********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>01</cbc:ID>
                    <cbc:Name>IVA</cbc:Name>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
        </cac:Party>
    </cac:AccountingSupplierParty>
    
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cac:PartyName>
                <cbc:Name>Cliente de Prueba S.A.S.</cbc:Name>
            </cac:PartyName>
            <cac:PartyTaxScheme>
                <cbc:RegistrationName>Cliente de Prueba S.A.S.</cbc:RegistrationName>
                <cbc:CompanyID schemeID="31">*********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>01</cbc:ID>
                    <cbc:Name>IVA</cbc:Name>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
        </cac:Party>
    </cac:AccountingCustomerParty>
    
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="COP">100000.00</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="COP">100000.00</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="COP">119000.00</cbc:TaxInclusiveAmount>
        <cbc:PayableAmount currencyID="COP">119000.00</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    
    <cac:InvoiceLine>
        <cbc:ID>1</cbc:ID>
        <cbc:InvoicedQuantity unitCode="NIU">1</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="COP">100000.00</cbc:LineExtensionAmount>
        <cac:Item>
            <cbc:Description>Servicio de consultoría</cbc:Description>
            <cbc:Name>Consultoría Técnica</cbc:Name>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="COP">100000.00</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    
</Invoice>'''
    
    return xml_content

def create_test_zip_files():
    """Crear archivos ZIP de prueba"""
    
    print("=== CREANDO ARCHIVOS ZIP DE PRUEBA ===")
    
    # Crear directorio temp_files si no existe
    temp_files_path = Path('temp_files')
    temp_files_path.mkdir(exist_ok=True)
    
    # Datos de prueba
    test_files = [
        {
            'filename': 'factura_acme_001.zip',
            'xml_name': 'factura_acme_001.xml',
            'cufe': 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',
            'issuer': 'ACME Corporation S.A.S.',
            'doc_number': 'FV-ACME-001'
        },
        {
            'filename': 'factura_xtu_002.zip',
            'xml_name': 'factura_xtu_002.xml',
            'cufe': 'b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567',
            'issuer': 'XTU Consulting S.A.S.',
            'doc_number': 'FV-XTU-002'
        },
        {
            'filename': 'invoice_test_003.zip',
            'xml_name': 'invoice_test_003.xml',
            'cufe': 'c3d4e5f6789012345678901234567890abcdef1234567890abcdef12345678',
            'issuer': 'Test Solutions S.A.S.',
            'doc_number': 'INV-TEST-003'
        },
        {
            'filename': 'documento_electronico_004.zip',
            'xml_name': 'documento_004.xml',
            'cufe': 'd4e5f6789012345678901234567890abcdef1234567890abcdef123456789',
            'issuer': 'Documentos Electrónicos S.A.S.',
            'doc_number': 'DOC-ELEC-004'
        },
        {
            'filename': 'factura_con_pdf_005.zip',
            'xml_name': 'factura_005.xml',
            'cufe': 'e5f6789012345678901234567890abcdef1234567890abcdef1234567890',
            'issuer': 'Empresa con PDF S.A.S.',
            'doc_number': 'FV-PDF-005',
            'include_pdf': True
        }
    ]
    
    created_files = []
    
    for test_file in test_files:
        zip_path = temp_files_path / test_file['filename']
        
        print(f"\n📦 Creando: {test_file['filename']}")
        
        # Crear contenido XML
        xml_content = create_test_xml_content(
            test_file['cufe'],
            test_file['issuer'],
            test_file['doc_number']
        )
        
        # Crear archivo ZIP
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
            # Agregar archivo XML
            zf.writestr(test_file['xml_name'], xml_content)
            print(f"   ✅ Agregado: {test_file['xml_name']}")
            
            # Agregar PDF si se especifica
            if test_file.get('include_pdf', False):
                pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF"
                zf.writestr(f"{test_file['doc_number']}.pdf", pdf_content)
                print(f"   ✅ Agregado: {test_file['doc_number']}.pdf")
        
        file_size = zip_path.stat().st_size
        print(f"   📊 Tamaño: {file_size} bytes")
        print(f"   🎯 CUFE: {test_file['cufe'][:20]}...")
        
        created_files.append(zip_path)
    
    print(f"\n✅ CREACIÓN COMPLETADA")
    print(f"📦 Archivos ZIP creados: {len(created_files)}")
    print(f"📁 Ubicación: {temp_files_path.absolute()}")
    
    # Verificar archivos creados
    print(f"\n🔍 VERIFICACIÓN:")
    for zip_file in created_files:
        try:
            with zipfile.ZipFile(zip_file, 'r') as zf:
                files_in_zip = zf.namelist()
                print(f"   {zip_file.name}: {len(files_in_zip)} archivos - {files_in_zip}")
        except Exception as e:
            print(f"   ❌ Error verificando {zip_file.name}: {e}")
    
    return created_files

def main():
    """Función principal"""
    print("=" * 60)
    print("CREADOR DE ARCHIVOS ZIP DE PRUEBA")
    print("=" * 60)
    
    created_files = create_test_zip_files()
    
    print(f"\n💡 PRÓXIMOS PASOS:")
    print("1. Ejecutar: python simulate_zip_email_processing.py")
    print("2. Probar la detección de ZIP con estos archivos")
    print("3. Iniciar los servicios y probar el procesamiento real")

if __name__ == "__main__":
    main()
