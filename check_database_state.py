#!/usr/bin/env python3
"""
Script to check the current database state and identify client_id issues
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set the database URL environment variable
os.environ['DATABASE_URL'] = 'postgresql://cufe_user:cufe_password@localhost:5432/cufe_db'

from shared.database.models import EmailRecord, CUFERecord, Client, User
from shared.database.connection import get_db

def check_database_state():
    """Check the current state of the database"""
    print("🔍 Checking database state...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Check clients
        print("\n📊 CLIENTS:")
        clients = db.query(Client).all()
        for client in clients:
            print(f"  ID: {client.id}, Client ID: {client.client_id}, Company: {client.company_name}")
        
        # Check users
        print("\n👥 USERS:")
        users = db.query(User).all()
        for user in users:
            print(f"  ID: {user.id}, Username: {user.username}, Client ID: {user.client_id}")
        
        # Check email records
        print("\n📧 EMAIL RECORDS:")
        email_records = db.query(EmailRecord).order_by(EmailRecord.id.desc()).limit(10).all()
        for record in email_records:
            print(f"  ID: {record.id}, Email ID: {record.email_id[:20]}..., Client ID: {record.client_id}, Status: {record.processing_status}")
        
        # Check CUFE records with client association
        print("\n🧾 CUFE RECORDS WITH CLIENT INFO:")
        cufe_query = db.query(CUFERecord, EmailRecord.client_id).join(EmailRecord).order_by(CUFERecord.id.desc()).limit(5)
        cufe_records = cufe_query.all()
        for cufe_record, client_id in cufe_records:
            print(f"  CUFE: {cufe_record.cufe_value[:20]}..., Issuer: {cufe_record.issuer_name}, Client ID: {client_id}")
        
        # Count records without client_id
        print("\n⚠️  RECORDS WITHOUT CLIENT_ID:")
        emails_without_client = db.query(EmailRecord).filter(EmailRecord.client_id.is_(None)).count()
        print(f"  Email records without client_id: {emails_without_client}")
        
        # Count total records
        print("\n📈 TOTALS:")
        total_emails = db.query(EmailRecord).count()
        total_cufes = db.query(CUFERecord).count()
        print(f"  Total email records: {total_emails}")
        print(f"  Total CUFE records: {total_cufes}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking database state: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = check_database_state()
    if success:
        print("\n✅ Database state check completed")
    else:
        print("\n❌ Database state check failed")
        sys.exit(1)
