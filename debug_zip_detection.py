#!/usr/bin/env python3
"""
Script para diagnosticar la detección de archivos ZIP en emails
"""

import os
import sys
import requests
import json
from pathlib import Path

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def enable_zip_detection_logging():
    """Habilitar logging detallado para detección de ZIP"""
    print("=== HABILITANDO LOGGING DE DETECCIÓN ZIP ===")
    
    # Verificar si los servicios están activos
    try:
        response = requests.get('http://localhost:8001/health', timeout=5)
        if response.status_code != 200:
            print("❌ Mailbox service no está activo")
            return False
    except:
        print("❌ No se puede conectar al mailbox service")
        return False
    
    print("✅ Mailbox service está activo")
    return True

def test_zip_detection_with_existing_files():
    """Probar detección de ZIP con archivos existentes"""
    print("\n=== PROBANDO DETECCIÓN DE ZIP CON ARCHIVOS EXISTENTES ===")
    
    # Verificar archivos ZIP en temp_files
    temp_files_path = Path('temp_files')
    if not temp_files_path.exists():
        print("❌ No existe directorio temp_files")
        return
    
    zip_files = list(temp_files_path.glob('*.zip'))
    print(f"Archivos ZIP encontrados: {len(zip_files)}")
    
    if not zip_files:
        print("❌ No hay archivos ZIP para probar")
        return
    
    # Mostrar información de algunos archivos ZIP
    for i, zip_file in enumerate(zip_files[:5]):
        print(f"\n📁 Archivo {i+1}: {zip_file.name}")
        print(f"   Tamaño: {zip_file.stat().st_size} bytes")
        
        # Verificar contenido
        try:
            import zipfile
            with zipfile.ZipFile(zip_file, 'r') as zf:
                files_in_zip = zf.namelist()
                print(f"   Archivos en ZIP: {len(files_in_zip)}")
                for j, file_name in enumerate(files_in_zip[:3]):
                    print(f"      - {file_name}")
                
                # Verificar si hay archivos XML
                xml_files = [f for f in files_in_zip if f.lower().endswith('.xml')]
                print(f"   Archivos XML: {len(xml_files)}")
                
        except Exception as e:
            print(f"   ❌ Error leyendo ZIP: {e}")

def test_email_processing_with_debug():
    """Probar procesamiento de emails con debug habilitado"""
    print("\n=== PROBANDO PROCESAMIENTO DE EMAILS CON DEBUG ===")
    
    # Configurar request de prueba
    test_request = {
        "email_host": "imap.gmail.com",
        "email_port": 993,
        "email_username": "<EMAIL>",  # Esto debería venir de configuración
        "email_password": "test_password",     # Esto debería venir de configuración
        "use_ssl": True,
        "folder": "INBOX",
        "date_from": "2025-09-01",
        "date_to": "2025-09-17",
        "max_emails": 10,
        "client_id": 1,
        "zip_only": True
    }
    
    print("⚠️  NOTA: Para probar realmente necesitas configurar credenciales de email válidas")
    print("Configuración de prueba:")
    print(f"  Host: {test_request['email_host']}")
    print(f"  Puerto: {test_request['email_port']}")
    print(f"  Carpeta: {test_request['folder']}")
    print(f"  Rango de fechas: {test_request['date_from']} a {test_request['date_to']}")

def check_database_for_zip_emails():
    """Verificar en la base de datos si hay emails con ZIP"""
    print("\n=== VERIFICANDO BASE DE DATOS ===")
    
    try:
        # Intentar obtener estadísticas
        response = requests.get('http://localhost:8000/stats', timeout=10)
        
        if response.status_code == 200:
            stats = response.json()
            print("📊 Estadísticas de la base de datos:")
            print(f"   Total emails: {stats.get('total_emails', 'N/A')}")
            print(f"   Emails con ZIP: {stats.get('emails_with_zip', 'N/A')}")
            print(f"   Registros CUFE: {stats.get('total_cufe_records', 'N/A')}")
            print(f"   Archivos ZIP: {stats.get('total_zip_files', 'N/A')}")
            
            # Si hay pocos emails con ZIP, eso confirma el problema
            total_emails = stats.get('total_emails', 0)
            emails_with_zip = stats.get('emails_with_zip', 0)
            
            if total_emails > 0 and emails_with_zip == 0:
                print("\n🔍 PROBLEMA IDENTIFICADO:")
                print(f"   Se han procesado {total_emails} emails pero ninguno tiene ZIP detectado")
                print("   Esto confirma que hay un problema en la detección de archivos ZIP")
            elif emails_with_zip > 0:
                print(f"\n✅ Se detectaron {emails_with_zip} emails con ZIP de {total_emails} totales")
            
        else:
            print(f"❌ Error obteniendo estadísticas: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error conectando a la API: {e}")

def suggest_solutions():
    """Sugerir soluciones basadas en el diagnóstico"""
    print("\n=== POSIBLES SOLUCIONES ===")
    
    print("1. 🔧 HABILITAR LOGGING DETALLADO:")
    print("   - Modificar la configuración del email service para habilitar 'log_zip_detection'")
    print("   - Esto mostrará exactamente qué está buscando en cada email")
    
    print("\n2. 🔍 VERIFICAR DETECCIÓN DE ADJUNTOS:")
    print("   - El sistema busca 'application/zip' en el bodystructure")
    print("   - También busca '.zip' en nombres de archivo")
    print("   - Algunos servidores pueden usar 'application/octet-stream'")
    
    print("\n3. 📧 PROBAR CON EMAILS REALES:")
    print("   - Configurar credenciales de email válidas")
    print("   - Procesar emails que sabemos que tienen archivos ZIP")
    
    print("\n4. 🛠️ MEJORAR DETECCIÓN:")
    print("   - Ampliar los tipos MIME que se consideran ZIP")
    print("   - Mejorar la lógica de parsing del bodystructure")
    
    print("\n5. 📁 USAR ARCHIVOS EXISTENTES:")
    print("   - Los archivos ZIP en temp_files sugieren que en algún momento se descargaron")
    print("   - Verificar si estos archivos se procesaron correctamente")

def main():
    """Función principal de diagnóstico"""
    print("=" * 60)
    print("DIAGNÓSTICO DE DETECCIÓN DE ARCHIVOS ZIP")
    print("=" * 60)
    
    # 1. Verificar servicios
    if not enable_zip_detection_logging():
        print("\n❌ No se pueden ejecutar todas las pruebas sin servicios activos")
        return
    
    # 2. Verificar archivos ZIP existentes
    test_zip_detection_with_existing_files()
    
    # 3. Verificar base de datos
    check_database_for_zip_emails()
    
    # 4. Mostrar configuración de prueba
    test_email_processing_with_debug()
    
    # 5. Sugerir soluciones
    suggest_solutions()
    
    print("\n" + "=" * 60)
    print("DIAGNÓSTICO COMPLETADO")
    print("=" * 60)

if __name__ == "__main__":
    main()
