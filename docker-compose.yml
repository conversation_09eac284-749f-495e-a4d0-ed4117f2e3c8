version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: cufe_postgres
    env_file: .env
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-cufe_db}
      POSTGRES_USER: ${POSTGRES_USER:-cufe_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-cufe_password_123}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - cufe_network

  # Mailbox Service
  mailbox-service:
    build:
      context: .
      dockerfile: services/mailbox_service/Dockerfile
    container_name: cufe_mailbox_service
    env_file: .env
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-cufe_user}:${POSTGRES_PASSWORD:-cufe_password_123}@postgres:5432/${POSTGRES_DB:-cufe_db}
    depends_on:
      - postgres
    networks:
      - cufe_network
    volumes:
      - ./temp_files:/app/temp_files

  # File Processing Service
  file-processing-service:
    build:
      context: .
      dockerfile: services/file_processing_service/Dockerfile
    container_name: cufe_file_processing_service
    env_file: .env
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-cufe_user}:${POSTGRES_PASSWORD:-cufe_password_123}@postgres:5432/${POSTGRES_DB:-cufe_db}
    depends_on:
      - postgres
    networks:
      - cufe_network
    volumes:
      - ./temp_files:/app/temp_files
      - ./processed_files:/app/processed_files
      - ./extracted_files:/app/extracted_files

  # Extraction Service
  extraction-service:
    build:
      context: .
      dockerfile: services/extraction_service/Dockerfile
    container_name: cufe_extraction_service
    env_file: .env
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-cufe_user}:${POSTGRES_PASSWORD:-cufe_password_123}@postgres:5432/${POSTGRES_DB:-cufe_db}
    depends_on:
      - postgres
    networks:
      - cufe_network
    volumes:
      - ./processed_files:/app/processed_files
      - ./extracted_files:/app/extracted_files

  # Main API Service
  api-service:
    build:
      context: .
      dockerfile: services/api_service/Dockerfile
    container_name: cufe_api_service
    env_file: .env
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-cufe_user}:${POSTGRES_PASSWORD:-cufe_password_123}@postgres:5432/${POSTGRES_DB:-cufe_db}
      - MAILBOX_SERVICE_URL=http://mailbox-service:8001
      - FILE_PROCESSING_SERVICE_URL=http://file-processing-service:8002
      - EXTRACTION_SERVICE_URL=http://extraction-service:8003
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - mailbox-service
      - file-processing-service
      - extraction-service
    networks:
      - cufe_network

  # Frontend Service
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    container_name: cufe_frontend
    env_file: .env
    ports:
      - "3000:80"
    depends_on:
      - api-service
    networks:
      - cufe_network

volumes:
  postgres_data:

networks:
  cufe_network:
    driver: bridge
