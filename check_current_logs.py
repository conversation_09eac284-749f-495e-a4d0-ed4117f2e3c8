#!/usr/bin/env python3
"""
Script para revisar los logs actuales y entender qué está pasando
"""

import os
import sys
from pathlib import Path
from datetime import datetime

def check_log_files():
    """Revisar archivos de log existentes"""
    print("=== REVISANDO ARCHIVOS DE LOG ===")
    
    # Posibles ubicaciones de logs
    log_locations = [
        Path('logs'),
        Path('services/mailbox_service/logs'),
        Path('services/api_service/logs'),
        Path('.'),  # Directorio actual
    ]
    
    found_logs = []
    
    for log_dir in log_locations:
        if log_dir.exists():
            log_files = list(log_dir.glob('*.log'))
            if log_files:
                found_logs.extend(log_files)
                print(f"📁 {log_dir}: {len(log_files)} archivos de log")
                for log_file in log_files:
                    size = log_file.stat().st_size
                    modified = datetime.fromtimestamp(log_file.stat().st_mtime)
                    print(f"   📄 {log_file.name}: {size} bytes, modificado {modified}")
    
    if not found_logs:
        print("❌ No se encontraron archivos de log")
        return []
    
    return found_logs

def analyze_recent_log_entries(log_files, search_terms=None):
    """Analizar entradas recientes en los logs"""
    if not search_terms:
        search_terms = [
            'DEBUGGING',
            'ZIP',
            'has_zip_attachment',
            'bodystructure',
            'attachment',
            'process-emails'
        ]
    
    print(f"\n=== ANALIZANDO LOGS RECIENTES ===")
    print(f"Buscando términos: {search_terms}")
    
    recent_entries = []
    
    for log_file in log_files:
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # Buscar líneas recientes (últimas 100 líneas)
            recent_lines = lines[-100:] if len(lines) > 100 else lines
            
            for i, line in enumerate(recent_lines):
                line_lower = line.lower()
                for term in search_terms:
                    if term.lower() in line_lower:
                        recent_entries.append({
                            'file': log_file.name,
                            'line_num': len(lines) - len(recent_lines) + i + 1,
                            'content': line.strip(),
                            'term': term
                        })
                        break
        
        except Exception as e:
            print(f"❌ Error leyendo {log_file}: {e}")
    
    if recent_entries:
        print(f"\n📊 ENCONTRADAS {len(recent_entries)} ENTRADAS RELEVANTES:")
        
        # Agrupar por término
        by_term = {}
        for entry in recent_entries:
            term = entry['term']
            if term not in by_term:
                by_term[term] = []
            by_term[term].append(entry)
        
        for term, entries in by_term.items():
            print(f"\n🔍 TÉRMINO '{term}' ({len(entries)} entradas):")
            for entry in entries[-3:]:  # Mostrar últimas 3
                print(f"   📄 {entry['file']}:{entry['line_num']}")
                print(f"      {entry['content']}")
    else:
        print("❌ No se encontraron entradas relevantes en los logs")
    
    return recent_entries

def check_database_records():
    """Revisar registros recientes en la base de datos"""
    print(f"\n=== REVISANDO BASE DE DATOS ===")
    
    try:
        import sqlite3
        
        # Buscar archivo de base de datos
        db_files = list(Path('.').glob('*.db')) + list(Path('.').glob('*.sqlite'))
        
        if not db_files:
            print("❌ No se encontró archivo de base de datos")
            return
        
        db_file = db_files[0]
        print(f"📊 Usando base de datos: {db_file}")
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # Revisar emails recientes
        cursor.execute("""
            SELECT id, email_id, sender, subject, has_zip_attachment, processing_status, 
                   processed_date, error_message
            FROM email_records 
            ORDER BY id DESC 
            LIMIT 10
        """)
        
        records = cursor.fetchall()
        
        if records:
            print(f"\n📧 ÚLTIMOS {len(records)} EMAILS PROCESADOS:")
            for record in records:
                id, email_id, sender, subject, has_zip, status, processed, error = record
                print(f"\n   📨 ID: {id}")
                print(f"      Email ID: {email_id}")
                print(f"      De: {sender}")
                print(f"      Asunto: {subject[:50]}...")
                print(f"      Tiene ZIP: {has_zip}")
                print(f"      Estado: {status}")
                if error:
                    print(f"      Error: {error}")
        else:
            print("❌ No hay registros de emails en la base de datos")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error accediendo a la base de datos: {e}")

def create_enhanced_debug_endpoint():
    """Crear un endpoint de debug que no requiera credenciales"""
    print(f"\n=== CREANDO ENDPOINT DE DEBUG ===")
    
    debug_script = '''#!/usr/bin/env python3
"""
Script para crear un endpoint de debug temporal
"""

import requests
import json

def trigger_debug_mode():
    """Activar modo debug en el mailbox service"""
    
    # Intentar activar logging detallado via endpoint
    try:
        response = requests.post(
            'http://localhost:8001/debug/enable-detailed-logging',
            json={"enable": True},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Logging detallado activado via endpoint")
        else:
            print(f"⚠️  Endpoint debug no disponible: {response.status_code}")
    except:
        print("⚠️  Endpoint debug no disponible")
    
    # Intentar obtener estadísticas del servicio
    try:
        response = requests.get(
            'http://localhost:8001/stats',
            timeout=10
        )
        
        if response.status_code == 200:
            stats = response.json()
            print("📊 ESTADÍSTICAS DEL SERVICIO:")
            print(f"   {json.dumps(stats, indent=2)}")
        else:
            print(f"⚠️  Estadísticas no disponibles: {response.status_code}")
    except:
        print("⚠️  Estadísticas no disponibles")

if __name__ == "__main__":
    trigger_debug_mode()
'''
    
    with open('trigger_debug.py', 'w', encoding='utf-8') as f:
        f.write(debug_script)
    
    print("✅ Script de debug creado: trigger_debug.py")

def main():
    """Función principal"""
    print("=" * 60)
    print("REVISIÓN DE LOGS Y ESTADO ACTUAL")
    print("=" * 60)
    
    # 1. Revisar archivos de log
    log_files = check_log_files()
    
    # 2. Analizar entradas recientes
    if log_files:
        analyze_recent_log_entries(log_files)
    
    # 3. Revisar base de datos
    check_database_records()
    
    # 4. Crear herramientas de debug
    create_enhanced_debug_endpoint()
    
    print(f"\n{'='*60}")
    print("RESUMEN Y PRÓXIMOS PASOS")
    print(f"{'='*60}")
    
    print(f"\n💡 PARA ENTENDER EL PROBLEMA:")
    print("1. Si hay logs con 'DEBUGGING -', revisar qué muestran")
    print("2. Si hay emails con has_zip_attachment=False, ese es el problema")
    print("3. Si no hay logs recientes, el frontend no está enviando requests")
    
    print(f"\n🔧 ACCIONES RECOMENDADAS:")
    print("1. Ejecutar: python trigger_debug.py")
    print("2. Hacer que el frontend procese algunos emails")
    print("3. Revisar logs inmediatamente después")
    print("4. Comparar bodystructure de emails reales vs simulados")
    
    print(f"\n📋 SI EL FRONTEND YA ESTÁ CONFIGURADO:")
    print("   El problema es que la detección de ZIP falla en emails reales")
    print("   Necesitamos ver exactamente qué formato tienen esos emails")
    print("   Los logs con 'DEBUGGING -' mostrarán la información clave")

if __name__ == "__main__":
    main()
