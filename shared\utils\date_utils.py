"""
Date utilities for email processing and IMAP date filtering
"""

from datetime import datetime, timedelta
from typing import Optional, Tuple
import re


class DateRangeHelper:
    """
    Helper class for creating and managing date ranges for email filtering
    """
    
    # IMAP date format
    IMAP_DATE_FORMAT = "%d-%b-%Y"
    
    @classmethod
    def create_imap_date_filter(cls, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> str:
        """
        Create an IMAP date filter string from datetime objects
        
        Args:
            start_date: Start date for filtering (inclusive)
            end_date: End date for filtering (exclusive)
            
        Returns:
            IMAP date filter string
        """
        criteria = []
        
        if start_date:
            formatted_date = start_date.strftime(cls.IMAP_DATE_FORMAT)
            criteria.append(f"SINCE {formatted_date}")
        
        if end_date:
            formatted_date = end_date.strftime(cls.IMAP_DATE_FORMAT)
            criteria.append(f"BEFORE {formatted_date}")
        
        return " ".join(criteria)
    
    @classmethod
    def parse_imap_date(cls, date_str: str) -> datetime:
        """
        Parse IMAP date string to datetime object
        
        Args:
            date_str: Date string in IMAP format (DD-MMM-YYYY)
            
        Returns:
            datetime object
            
        Raises:
            ValueError: If date format is invalid
        """
        return datetime.strptime(date_str, cls.IMAP_DATE_FORMAT)
    
    @classmethod
    def validate_imap_date_format(cls, date_str: str) -> bool:
        """
        Validate IMAP date format (DD-MMM-YYYY)
        
        Args:
            date_str: Date string to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            cls.parse_imap_date(date_str)
            return True
        except ValueError:
            return False
    
    @classmethod
    def get_last_n_days_filter(cls, days: int) -> str:
        """
        Create a date filter for the last N days
        
        Args:
            days: Number of days to go back
            
        Returns:
            IMAP date filter string
        """
        start_date = datetime.now() - timedelta(days=days)
        return cls.create_imap_date_filter(start_date=start_date)
    
    @classmethod
    def get_current_month_filter(cls) -> str:
        """
        Create a date filter for the current month
        
        Returns:
            IMAP date filter string
        """
        now = datetime.now()
        start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        return cls.create_imap_date_filter(start_date=start_of_month)
    
    @classmethod
    def get_current_week_filter(cls) -> str:
        """
        Create a date filter for the current week (Monday to Sunday)
        
        Returns:
            IMAP date filter string
        """
        now = datetime.now()
        # Get Monday of current week
        days_since_monday = now.weekday()
        start_of_week = now - timedelta(days=days_since_monday)
        start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
        return cls.create_imap_date_filter(start_date=start_of_week)
    
    @classmethod
    def parse_date_range_string(cls, date_range: str) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        Parse a date range string into start and end dates
        
        Args:
            date_range: Date range string (e.g., "2024-01-01 to 2024-01-31", "last 7 days")
            
        Returns:
            Tuple of (start_date, end_date)
        """
        date_range = date_range.strip().lower()
        
        # Handle "last N days" format
        last_days_match = re.match(r'last (\d+) days?', date_range)
        if last_days_match:
            days = int(last_days_match.group(1))
            start_date = datetime.now() - timedelta(days=days)
            return start_date, None
        
        # Handle "current week"
        if date_range in ['current week', 'this week']:
            now = datetime.now()
            days_since_monday = now.weekday()
            start_of_week = now - timedelta(days=days_since_monday)
            start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
            return start_of_week, None
        
        # Handle "current month"
        if date_range in ['current month', 'this month']:
            now = datetime.now()
            start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            return start_of_month, None
        
        # Handle "YYYY-MM-DD to YYYY-MM-DD" format
        date_range_match = re.match(r'(\d{4}-\d{2}-\d{2})\s+to\s+(\d{4}-\d{2}-\d{2})', date_range)
        if date_range_match:
            start_str, end_str = date_range_match.groups()
            start_date = datetime.strptime(start_str, "%Y-%m-%d")
            end_date = datetime.strptime(end_str, "%Y-%m-%d")
            return start_date, end_date
        
        return None, None
    
    @classmethod
    def get_preset_filters(cls) -> dict:
        """
        Get a dictionary of preset date filters
        
        Returns:
            Dictionary with preset names and their corresponding filters
        """
        return {
            "last_7_days": cls.get_last_n_days_filter(7),
            "last_30_days": cls.get_last_n_days_filter(30),
            "current_week": cls.get_current_week_filter(),
            "current_month": cls.get_current_month_filter(),
        }


def format_date_for_display(date: datetime) -> str:
    """
    Format a datetime object for user-friendly display
    
    Args:
        date: datetime object to format
        
    Returns:
        Formatted date string
    """
    return date.strftime("%B %d, %Y")


def format_date_range_for_display(start_date: Optional[datetime], end_date: Optional[datetime]) -> str:
    """
    Format a date range for user-friendly display
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        Formatted date range string
    """
    if start_date and end_date:
        return f"{format_date_for_display(start_date)} to {format_date_for_display(end_date)}"
    elif start_date:
        return f"Since {format_date_for_display(start_date)}"
    elif end_date:
        return f"Before {format_date_for_display(end_date)}"
    else:
        return "All emails"
