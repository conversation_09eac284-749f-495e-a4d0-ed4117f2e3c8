#!/usr/bin/env python3
"""
Script para crear un endpoint de debug temporal
"""

import requests
import json

def trigger_debug_mode():
    """Activar modo debug en el mailbox service"""
    
    # Intentar activar logging detallado via endpoint
    try:
        response = requests.post(
            'http://localhost:8001/debug/enable-detailed-logging',
            json={"enable": True},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Logging detallado activado via endpoint")
        else:
            print(f"⚠️  Endpoint debug no disponible: {response.status_code}")
    except:
        print("⚠️  Endpoint debug no disponible")
    
    # Intentar obtener estadísticas del servicio
    try:
        response = requests.get(
            'http://localhost:8001/stats',
            timeout=10
        )
        
        if response.status_code == 200:
            stats = response.json()
            print("📊 ESTADÍSTICAS DEL SERVICIO:")
            print(f"   {json.dumps(stats, indent=2)}")
        else:
            print(f"⚠️  Estadísticas no disponibles: {response.status_code}")
    except:
        print("⚠️  Estadísticas no disponibles")

if __name__ == "__main__":
    trigger_debug_mode()
