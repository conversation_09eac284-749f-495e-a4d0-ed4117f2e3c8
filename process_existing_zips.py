#!/usr/bin/env python3
"""
Script para procesar directamente los archivos ZIP existentes sin depender de la detección de emails
"""

import os
import sys
import zipfile
import uuid
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def process_zip_directly(zip_file_path, extract_to_base):
    """Procesar un archivo ZIP directamente"""
    try:
        print(f"\n📁 Procesando: {zip_file_path.name}")
        
        # Crear directorio de extracción único
        extraction_id = str(uuid.uuid4())
        extraction_dir = extract_to_base / extraction_id
        extraction_dir.mkdir(parents=True, exist_ok=True)
        
        # Extraer archivos
        extracted_files = []
        with zipfile.ZipFile(zip_file_path, 'r') as zip_file:
            for file_info in zip_file.filelist:
                if not file_info.is_dir():
                    extracted_path = zip_file.extract(file_info, extraction_dir)
                    extracted_files.append(Path(extracted_path))
        
        print(f"   ✅ Extraídos {len(extracted_files)} archivos")
        
        # Categorizar archivos
        xml_files = [f for f in extracted_files if f.suffix.lower() in ['.xml', '.xlm']]
        pdf_files = [f for f in extracted_files if f.suffix.lower() == '.pdf']
        other_files = [f for f in extracted_files if f not in xml_files and f not in pdf_files]
        
        print(f"   📄 XML: {len(xml_files)}, PDF: {len(pdf_files)}, Otros: {len(other_files)}")
        
        # Procesar archivos XML para extraer CUFE
        cufe_results = []
        for xml_file in xml_files:
            cufe_result = extract_cufe_from_xml(xml_file)
            cufe_results.append(cufe_result)
            
            if cufe_result['success']:
                print(f"   🎯 CUFE encontrado: {cufe_result['cufe'][:20]}...")
            else:
                print(f"   ❌ No se pudo extraer CUFE: {cufe_result['error']}")
        
        return {
            'success': True,
            'extraction_dir': extraction_dir,
            'extracted_files': len(extracted_files),
            'xml_files': len(xml_files),
            'pdf_files': len(pdf_files),
            'cufe_results': cufe_results
        }
        
    except Exception as e:
        print(f"   ❌ Error procesando ZIP: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def extract_cufe_from_xml(xml_file_path):
    """Extraer CUFE de un archivo XML usando múltiples métodos"""
    try:
        # Leer contenido del archivo
        with open(xml_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Método 1: Buscar CUFE en el contenido como texto
        cufe_patterns = [
            'CUFE',
            'UUID',
            'schemeName="CUFE"',
            'schemeID="CUFE"'
        ]
        
        cufe_value = None
        
        # Buscar patrones simples primero
        if 'CUFE' in content:
            # Buscar entre tags XML
            import re
            
            # Patrón para UUID con schemeName="CUFE"
            pattern1 = r'<[^>]*UUID[^>]*schemeName="CUFE"[^>]*>([^<]+)</[^>]*UUID[^>]*>'
            match1 = re.search(pattern1, content, re.IGNORECASE)
            
            if match1:
                cufe_value = match1.group(1).strip()
            else:
                # Patrón más simple para cualquier tag que contenga CUFE
                pattern2 = r'<[^>]*CUFE[^>]*>([^<]+)</[^>]*>'
                match2 = re.search(pattern2, content, re.IGNORECASE)
                
                if match2:
                    cufe_value = match2.group(1).strip()
                else:
                    # Buscar CUFE como atributo
                    pattern3 = r'CUFE["\s]*=["\']\s*([^"\']+)["\']\s*'
                    match3 = re.search(pattern3, content, re.IGNORECASE)
                    
                    if match3:
                        cufe_value = match3.group(1).strip()
        
        # Método 2: Usar lxml si está disponible
        if not cufe_value:
            try:
                from lxml import etree
                
                # Parse XML
                root = etree.fromstring(content.encode('utf-8'))
                
                # Namespaces comunes para facturas colombianas
                namespaces = {
                    'cbc': 'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2',
                    'cac': 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2',
                    'ext': 'urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2',
                    'sts': 'dian:gov:co:facturaelectronica:Structures-2-1',
                    'invoice': 'urn:oasis:names:specification:ubl:schema:xsd:Invoice-2'
                }
                
                # Buscar CUFE con XPath
                xpath_patterns = [
                    '//cbc:UUID[@schemeName="CUFE"]',
                    '//cbc:UUID',
                    '//*[local-name()="UUID" and @schemeName="CUFE"]',
                    '//*[local-name()="CUFE"]'
                ]
                
                for xpath in xpath_patterns:
                    try:
                        elements = root.xpath(xpath, namespaces=namespaces)
                        if elements and elements[0].text:
                            cufe_value = elements[0].text.strip()
                            break
                    except:
                        continue
                        
            except ImportError:
                pass  # lxml no disponible
            except Exception as e:
                pass  # Error en parsing XML
        
        if cufe_value and len(cufe_value) > 10:  # CUFE debe tener longitud mínima
            return {
                'success': True,
                'cufe': cufe_value,
                'xml_file': str(xml_file_path)
            }
        else:
            return {
                'success': False,
                'error': 'CUFE no encontrado o inválido',
                'xml_file': str(xml_file_path)
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'xml_file': str(xml_file_path)
        }

def process_all_existing_zips():
    """Procesar todos los archivos ZIP existentes"""
    print("=" * 60)
    print("PROCESAMIENTO DIRECTO DE ARCHIVOS ZIP EXISTENTES")
    print("=" * 60)
    
    # Directorios
    temp_files_path = Path('temp_files')
    extract_base_path = Path('processed_files/extracted_files')
    
    # Crear directorio de extracción si no existe
    extract_base_path.mkdir(parents=True, exist_ok=True)
    
    # Encontrar archivos ZIP
    if not temp_files_path.exists():
        print("❌ No existe directorio temp_files")
        return
    
    zip_files = list(temp_files_path.glob('*.zip'))
    print(f"📦 Archivos ZIP encontrados: {len(zip_files)}")
    
    if not zip_files:
        print("❌ No hay archivos ZIP para procesar")
        return
    
    # Procesar cada archivo ZIP
    total_processed = 0
    total_xml_files = 0
    total_cufe_found = 0
    
    for i, zip_file in enumerate(zip_files, 1):
        print(f"\n[{i}/{len(zip_files)}] {'-' * 40}")
        
        result = process_zip_directly(zip_file, extract_base_path)
        
        if result['success']:
            total_processed += 1
            total_xml_files += result['xml_files']
            
            # Contar CUFEs encontrados
            cufe_found = sum(1 for r in result['cufe_results'] if r['success'])
            total_cufe_found += cufe_found
            
        # Pausa pequeña para no sobrecargar
        if i % 10 == 0:
            print(f"\n⏸️  Pausa después de {i} archivos...")
    
    # Resumen final
    print(f"\n{'=' * 60}")
    print("RESUMEN DEL PROCESAMIENTO")
    print(f"{'=' * 60}")
    print(f"📦 Total archivos ZIP: {len(zip_files)}")
    print(f"✅ Procesados exitosamente: {total_processed}")
    print(f"📄 Total archivos XML: {total_xml_files}")
    print(f"🎯 Total CUFEs encontrados: {total_cufe_found}")
    
    if total_cufe_found > 0:
        print(f"\n🎉 ¡ÉXITO! Se encontraron {total_cufe_found} códigos CUFE")
        print("Los archivos han sido extraídos y procesados correctamente.")
    else:
        print(f"\n⚠️  No se encontraron códigos CUFE en los archivos XML")
        print("Esto puede indicar que los archivos no son facturas electrónicas válidas")
        print("o que tienen un formato diferente al esperado.")

def main():
    """Función principal"""
    process_all_existing_zips()

if __name__ == "__main__":
    main()
