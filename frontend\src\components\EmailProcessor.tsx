import React, { useState } from 'react'
import { fileProcessing<PERSON><PERSON>, ProcessEmailsRequest, ProcessEmailsResponse } from '../services/api'
import LoadingSpinner from './LoadingSpinner'

interface EmailProcessorProps {
  onProcessSuccess?: (result: ProcessEmailsResponse) => void
  onProcessError?: (error: string) => void
}

const EmailProcessor: React.FC<EmailProcessorProps> = ({ onProcessSuccess, onProcessError }) => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [selectedProvider, setSelectedProvider] = useState('Gmail')
  const [formData, setFormData] = useState<ProcessEmailsRequest>({
    email_host: 'imap.gmail.com',
    email_port: 993,
    email_username: '',
    email_password: '',
    use_ssl: true,
    folder: 'INBOX',
    date_filter: '',
    start_date: undefined,
    end_date: undefined
  })

  const [dateRangeType, setDateRangeType] = useState<'preset' | 'custom' | 'manual'>('preset')
  const [presetRange, setPresetRange] = useState<string>('last_7_days')
  const [dateValidationError, setDateValidationError] = useState<string>('')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked :
              type === 'number' ? parseInt(value) || 0 : value
    }))
  }

  const handleDateRangeTypeChange = (type: 'preset' | 'custom' | 'manual') => {
    setDateRangeType(type)

    // Clear date-related fields when switching types
    setFormData(prev => ({
      ...prev,
      date_filter: '',
      start_date: undefined,
      end_date: undefined
    }))
  }

  const handlePresetRangeChange = (preset: string) => {
    setPresetRange(preset)

    // Create date filter based on preset
    let dateFilter = ''
    const now = new Date()

    switch (preset) {
      case 'last_7_days':
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        dateFilter = `SINCE ${sevenDaysAgo.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }).replace(/\s/g, '-')}`
        break
      case 'last_30_days':
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        dateFilter = `SINCE ${thirtyDaysAgo.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }).replace(/\s/g, '-')}`
        break
      case 'current_week':
        const startOfWeek = new Date(now)
        startOfWeek.setDate(now.getDate() - now.getDay() + 1) // Monday
        dateFilter = `SINCE ${startOfWeek.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }).replace(/\s/g, '-')}`
        break
      case 'current_month':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
        dateFilter = `SINCE ${startOfMonth.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }).replace(/\s/g, '-')}`
        break
    }

    setFormData(prev => ({
      ...prev,
      date_filter: dateFilter
    }))
  }

  const validateDateRange = (startDate?: Date, endDate?: Date): string => {
    if (startDate && endDate) {
      if (startDate >= endDate) {
        return 'Start date must be before end date'
      }

      // Check if date range is too broad (more than 1 year)
      const oneYearInMs = 365 * 24 * 60 * 60 * 1000
      if (endDate.getTime() - startDate.getTime() > oneYearInMs) {
        return 'Date range cannot exceed 1 year'
      }
    }

    // Check if dates are too far in the future
    const now = new Date()
    if (startDate && startDate > now) {
      return 'Start date cannot be in the future'
    }
    if (endDate && endDate > now) {
      return 'End date cannot be in the future'
    }

    return ''
  }

  const validateManualDateFilter = (filter: string): string => {
    if (!filter.trim()) return ''

    // Basic validation for IMAP date format
    const imapDatePattern = /\b\d{2}-[A-Za-z]{3}-\d{4}\b/g
    const dateMatches = filter.match(imapDatePattern)

    if (filter.includes('SINCE') || filter.includes('BEFORE')) {
      if (!dateMatches || dateMatches.length === 0) {
        return 'Invalid date format. Use DD-MMM-YYYY (e.g., 01-Jan-2024)'
      }

      // Validate each date
      for (const dateStr of dateMatches) {
        try {
          const parts = dateStr.split('-')
          if (parts.length !== 3) throw new Error('Invalid format')

          const day = parseInt(parts[0])
          const month = parts[1]
          const year = parseInt(parts[2])

          if (day < 1 || day > 31) throw new Error('Invalid day')
          if (year < 1900 || year > new Date().getFullYear()) throw new Error('Invalid year')

          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
          if (!monthNames.includes(month)) throw new Error('Invalid month')

        } catch (e) {
          return `Invalid date format: ${dateStr}. Use DD-MMM-YYYY (e.g., 01-Jan-2024)`
        }
      }
    }

    return ''
  }

  const handleDateChange = (field: 'start_date' | 'end_date', value: string) => {
    const date = value ? new Date(value) : undefined
    const newFormData = {
      ...formData,
      [field]: date
    }

    // Validate date range
    const error = validateDateRange(
      field === 'start_date' ? date : formData.start_date,
      field === 'end_date' ? date : formData.end_date
    )

    setDateValidationError(error)
    setFormData(newFormData)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.email_username || !formData.email_password) {
      onProcessError?.('Please provide email credentials')
      return
    }

    // Validate date inputs based on current type
    let validationError = ''

    if (dateRangeType === 'custom') {
      validationError = validateDateRange(formData.start_date, formData.end_date)
    } else if (dateRangeType === 'manual') {
      validationError = validateManualDateFilter(formData.date_filter || '')
    }

    if (validationError) {
      setDateValidationError(validationError)
      onProcessError?.(validationError)
      return
    }

    try {
      setIsProcessing(true)
      setDateValidationError('')
      const result = await fileProcessingApi.processEmails(formData)
      onProcessSuccess?.(result)
    } catch (error: any) {
      console.error('Email processing error:', error)
      onProcessError?.(error.response?.data?.detail || 'Failed to process emails')
    } finally {
      setIsProcessing(false)
    }
  }

  const emailProviders = [
    { name: 'Gmail', host: 'imap.gmail.com', port: 993, ssl: true },
    { name: 'Outlook', host: 'outlook.office365.com', port: 993, ssl: true },
    { name: 'Yahoo', host: 'imap.mail.yahoo.com', port: 993, ssl: true },
    { name: 'Custom', host: '', port: 993, ssl: true }
  ]

  const handleProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const providerName = e.target.value
    setSelectedProvider(providerName)

    const provider = emailProviders.find(p => p.name === providerName)
    if (provider && provider.host) {
      setFormData(prev => ({
        ...prev,
        email_host: provider.host,
        email_port: provider.port,
        use_ssl: provider.ssl
      }))
    }
  }

  return (
    <div className="w-full max-w-2xl">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Email Provider Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Provider
          </label>
          <select
            value={selectedProvider}
            onChange={handleProviderChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {emailProviders.map(provider => (
              <option key={provider.name} value={provider.name}>
                {provider.name}
              </option>
            ))}
          </select>
        </div>

        {/* Show Email Host and Port fields only for Custom provider */}
        {selectedProvider === 'Custom' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Host
              </label>
              <input
                type="text"
                name="email_host"
                value={formData.email_host}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="imap.yourprovider.com"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Port
              </label>
              <input
                type="number"
                name="email_port"
                value={formData.email_port}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
          </div>
        )}

        {/* Credentials */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <input
            type="email"
            name="email_username"
            value={formData.email_username}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Password / App Password
          </label>
          <input
            type="password"
            name="email_password"
            value={formData.email_password}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Your email password or app password"
            required
          />
          <p className="mt-1 text-xs text-gray-500">
            For Gmail, use an App Password instead of your regular password
          </p>
        </div>

        {/* Advanced Options */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Folder
            </label>
            <input
              type="text"
              name="folder"
              value={formData.folder}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="INBOX"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Range Filter
            </label>

            {/* Date Range Type Selection */}
            <div className="mb-4">
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="dateRangeType"
                    value="preset"
                    checked={dateRangeType === 'preset'}
                    onChange={() => handleDateRangeTypeChange('preset')}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">Preset Range</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="dateRangeType"
                    value="custom"
                    checked={dateRangeType === 'custom'}
                    onChange={() => handleDateRangeTypeChange('custom')}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">Custom Dates</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="dateRangeType"
                    value="manual"
                    checked={dateRangeType === 'manual'}
                    onChange={() => handleDateRangeTypeChange('manual')}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">Manual Filter</span>
                </label>
              </div>
            </div>

            {/* Preset Range Selection */}
            {dateRangeType === 'preset' && (
              <select
                value={presetRange}
                onChange={(e) => handlePresetRangeChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="last_7_days">Last 7 days</option>
                <option value="last_30_days">Last 30 days</option>
                <option value="current_week">Current week</option>
                <option value="current_month">Current month</option>
              </select>
            )}

            {/* Custom Date Range */}
            {dateRangeType === 'custom' && (
              <div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Start Date
                    </label>
                    <input
                      type="date"
                      onChange={(e) => handleDateChange('start_date', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                        dateValidationError ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                      }`}
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      End Date
                    </label>
                    <input
                      type="date"
                      onChange={(e) => handleDateChange('end_date', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                        dateValidationError ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                      }`}
                    />
                  </div>
                </div>
                {dateValidationError && (
                  <p className="mt-2 text-sm text-red-600">{dateValidationError}</p>
                )}
              </div>
            )}

            {/* Manual Filter Input */}
            {dateRangeType === 'manual' && (
              <div>
                <input
                  type="text"
                  name="date_filter"
                  value={formData.date_filter}
                  onChange={(e) => {
                    handleInputChange(e)
                    // Real-time validation for manual filter
                    const error = validateManualDateFilter(e.target.value)
                    setDateValidationError(error)
                  }}
                  placeholder="e.g., SINCE 01-Jan-2024 BEFORE 31-Jan-2024"
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                    dateValidationError ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                  }`}
                />
                <p className="mt-1 text-xs text-gray-500">
                  Use IMAP format: SINCE DD-MMM-YYYY, BEFORE DD-MMM-YYYY
                </p>
                {dateValidationError && (
                  <p className="mt-2 text-sm text-red-600">{dateValidationError}</p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* SSL Option */}
        <div className="flex items-center">
          <input
            type="checkbox"
            name="use_ssl"
            checked={formData.use_ssl}
            onChange={handleInputChange}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label className="ml-2 block text-sm text-gray-700">
            Use SSL/TLS (recommended)
          </label>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isProcessing}
          className="w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Processing Emails...
            </>
          ) : (
            '🚀 Process Emails'
          )}
        </button>
      </form>
    </div>
  )
}

export default EmailProcessor
