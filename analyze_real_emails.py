#!/usr/bin/env python3
"""
Analizador de emails reales para entender por qué no se detectan ZIPs
"""

import requests
import json
import time

def analyze_recent_emails():
    """Analizar emails recientes procesados por el sistema"""
    
    print("🔍 ANALIZANDO EMAILS REALES DEL SISTEMA...")
    
    # Hacer un request que procese pocos emails para análisis
    analysis_request = {
        "max_emails": 3,  # Solo 3 emails para análisis detallado
        "client_id": 1,
        "zip_only": False,  # Procesar todos para ver qué pasa
        "enable_detailed_logging": True
    }
    
    try:
        # Usar el endpoint que ya está configurado en el frontend
        response = requests.post(
            'http://localhost:8001/process-emails-batch',
            json=analysis_request,
            timeout=60
        )
        
        print(f"📊 Respuesta del análisis: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ ANÁLISIS COMPLETADO:")
            print(f"   Emails procesados: {result.get('processed_count', 0)}")
            print(f"   Emails con ZIP detectados: {result.get('emails_with_zip_attachments', 0)}")
            
            # Mostrar detalles
            if 'sample_email_senders' in result:
                senders = result['sample_email_senders']
                subjects = result.get('sample_email_subjects', [])
                
                print(f"\n📧 EMAILS ANALIZADOS:")
                for i, sender in enumerate(senders):
                    subject = subjects[i] if i < len(subjects) else "N/A"
                    print(f"   {i+1}. De: {sender}")
                    print(f"      Asunto: {subject[:50]}...")
            
            # Revisar logs detallados
            print(f"\n💡 REVISAR LOGS:")
            print("   Los logs del mailbox service ahora contienen información detallada")
            print("   Buscar líneas que empiecen con 'DEBUGGING -'")
            print("   Esto mostrará exactamente qué está viendo el sistema en cada email")
            
        else:
            print(f"❌ Error en análisis: {response.status_code}")
            print(f"   Respuesta: {response.text}")
            
    except Exception as e:
        print(f"❌ Error conectando: {e}")

def main():
    print("=" * 60)
    print("ANÁLISIS DE EMAILS REALES")
    print("=" * 60)
    
    analyze_recent_emails()
    
    print(f"\n📋 PRÓXIMOS PASOS:")
    print("1. Revisar los logs del mailbox service")
    print("2. Buscar líneas 'DEBUGGING -' para ver qué detecta el sistema")
    print("3. Comparar con emails que SÍ deberían tener ZIP")

if __name__ == "__main__":
    main()
