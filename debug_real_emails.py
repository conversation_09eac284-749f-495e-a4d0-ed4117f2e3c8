#!/usr/bin/env python3
"""
Script para debuggear emails reales y entender por qué no se detectan los ZIP
"""

import os
import sys
import requests
import json
from pathlib import Path

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def enable_detailed_logging():
    """Habilitar logging súper detallado en el email service"""
    print("=== HABILITANDO LOGGING DETALLADO ===")
    
    email_service_path = Path('services/mailbox_service/email_service.py')
    
    if not email_service_path.exists():
        print("❌ No se encuentra email_service.py")
        return False
    
    # Leer el archivo
    with open(email_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Buscar la función _has_zip_in_bodystructure y agregar logging detallado
    old_function_start = "    def _has_zip_in_bodystructure(self, bodystructure: str) -> bool:"
    
    if old_function_start in content:
        # Encontrar toda la función
        lines = content.split('\n')
        start_idx = None
        end_idx = None
        
        for i, line in enumerate(lines):
            if old_function_start.strip() in line:
                start_idx = i
            elif start_idx is not None and line.strip() and not line.startswith('    ') and not line.startswith('\t'):
                end_idx = i
                break
        
        if start_idx is not None:
            if end_idx is None:
                end_idx = len(lines)
            
            # Nueva función con logging detallado
            new_function = '''    def _has_zip_in_bodystructure(self, bodystructure: str) -> bool:
        """
        Check if email has ZIP attachment based on BODYSTRUCTURE with detailed logging
        """
        try:
            bodystructure_lower = bodystructure.lower()
            
            # Log the full bodystructure for debugging
            logger.info(f"DEBUGGING - Full bodystructure: {bodystructure[:500]}...")
            
            zip_indicators = [
                'application/zip',
                'application/x-zip',
                'application/x-zip-compressed',
                'application/octet-stream',
                'application/x-compressed',
                'multipart/x-zip',
                'application/pkcs7-mime',
                'application/vnd.ms-office',
            ]
            
            zip_filename_patterns = [
                '.zip',
                'filename=',
                'name=',
                'attachment',
                'content-disposition',
                'factura',
                'invoice',
                'documento',
            ]
            
            # Check content type indicators
            found_content_indicators = []
            for indicator in zip_indicators:
                if indicator in bodystructure_lower:
                    found_content_indicators.append(indicator)
            
            # Check filename patterns
            found_filename_patterns = []
            for pattern in zip_filename_patterns:
                if pattern in bodystructure_lower:
                    found_filename_patterns.append(pattern)
            
            has_zip_content_type = len(found_content_indicators) > 0
            has_zip_filename = '.zip' in bodystructure_lower and len(found_filename_patterns) > 1
            
            # Detailed logging
            logger.info(f"DEBUGGING - Content indicators found: {found_content_indicators}")
            logger.info(f"DEBUGGING - Filename patterns found: {found_filename_patterns}")
            logger.info(f"DEBUGGING - Has ZIP content type: {has_zip_content_type}")
            logger.info(f"DEBUGGING - Has ZIP filename: {has_zip_filename}")
            
            result = has_zip_content_type or has_zip_filename
            logger.info(f"DEBUGGING - Final ZIP detection result: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in _has_zip_in_bodystructure: {str(e)}")
            return False'''
            
            # Reemplazar la función
            lines[start_idx:end_idx] = new_function.split('\n')
            
            # Escribir el archivo modificado
            with open(email_service_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            print("✅ Logging detallado habilitado en _has_zip_in_bodystructure")
            return True
    
    print("⚠️  No se pudo encontrar la función para modificar")
    return False

def add_email_content_logging():
    """Agregar logging del contenido completo de emails"""
    print("\n=== AGREGANDO LOGGING DE CONTENIDO DE EMAILS ===")
    
    email_service_path = Path('services/mailbox_service/email_service.py')
    
    # Leer el archivo
    with open(email_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Buscar donde agregar logging en _confirm_zip_attachment
    old_confirm_start = "    def _confirm_zip_attachment(self, mail: imaplib.IMAP4_SSL, email_id: str) -> bool:"
    
    if old_confirm_start in content:
        # Agregar logging después de obtener el mensaje
        old_fetch = "            status, msg_data = mail.fetch(email_id, '(RFC822)')"
        new_fetch = """            status, msg_data = mail.fetch(email_id, '(RFC822)')
            
            # DEBUGGING: Log email content for analysis
            if msg_data and msg_data[0] and msg_data[0][1]:
                email_content = msg_data[0][1]
                if isinstance(email_content, bytes):
                    email_content_str = email_content.decode('utf-8', errors='ignore')
                else:
                    email_content_str = str(email_content)
                
                logger.info(f"DEBUGGING - Email {email_id} full content (first 1000 chars): {email_content_str[:1000]}...")
                
                # Log specific sections
                content_lower = email_content_str.lower()
                if 'content-type:' in content_lower:
                    logger.info(f"DEBUGGING - Email {email_id} has Content-Type headers")
                if 'attachment' in content_lower:
                    logger.info(f"DEBUGGING - Email {email_id} has 'attachment' in content")
                if '.zip' in content_lower:
                    logger.info(f"DEBUGGING - Email {email_id} has '.zip' in content")
                if 'application/' in content_lower:
                    logger.info(f"DEBUGGING - Email {email_id} has 'application/' in content")"""
        
        if old_fetch in content:
            content = content.replace(old_fetch, new_fetch)
            
            # Escribir el archivo modificado
            with open(email_service_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Logging de contenido de emails agregado")
            return True
    
    print("⚠️  No se pudo agregar logging de contenido")
    return False

def create_real_email_analyzer():
    """Crear un analizador que capture emails reales del sistema"""
    print("\n=== CREANDO ANALIZADOR DE EMAILS REALES ===")
    
    analyzer_script = '''#!/usr/bin/env python3
"""
Analizador de emails reales para entender por qué no se detectan ZIPs
"""

import requests
import json
import time

def analyze_recent_emails():
    """Analizar emails recientes procesados por el sistema"""
    
    print("🔍 ANALIZANDO EMAILS REALES DEL SISTEMA...")
    
    # Hacer un request que procese pocos emails para análisis
    analysis_request = {
        "max_emails": 3,  # Solo 3 emails para análisis detallado
        "client_id": 1,
        "zip_only": False,  # Procesar todos para ver qué pasa
        "enable_detailed_logging": True
    }
    
    try:
        # Usar el endpoint que ya está configurado en el frontend
        response = requests.post(
            'http://localhost:8001/process-emails-batch',
            json=analysis_request,
            timeout=60
        )
        
        print(f"📊 Respuesta del análisis: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ ANÁLISIS COMPLETADO:")
            print(f"   Emails procesados: {result.get('processed_count', 0)}")
            print(f"   Emails con ZIP detectados: {result.get('emails_with_zip_attachments', 0)}")
            
            # Mostrar detalles
            if 'sample_email_senders' in result:
                senders = result['sample_email_senders']
                subjects = result.get('sample_email_subjects', [])
                
                print(f"\\n📧 EMAILS ANALIZADOS:")
                for i, sender in enumerate(senders):
                    subject = subjects[i] if i < len(subjects) else "N/A"
                    print(f"   {i+1}. De: {sender}")
                    print(f"      Asunto: {subject[:50]}...")
            
            # Revisar logs detallados
            print(f"\\n💡 REVISAR LOGS:")
            print("   Los logs del mailbox service ahora contienen información detallada")
            print("   Buscar líneas que empiecen con 'DEBUGGING -'")
            print("   Esto mostrará exactamente qué está viendo el sistema en cada email")
            
        else:
            print(f"❌ Error en análisis: {response.status_code}")
            print(f"   Respuesta: {response.text}")
            
    except Exception as e:
        print(f"❌ Error conectando: {e}")

def main():
    print("=" * 60)
    print("ANÁLISIS DE EMAILS REALES")
    print("=" * 60)
    
    analyze_recent_emails()
    
    print(f"\\n📋 PRÓXIMOS PASOS:")
    print("1. Revisar los logs del mailbox service")
    print("2. Buscar líneas 'DEBUGGING -' para ver qué detecta el sistema")
    print("3. Comparar con emails que SÍ deberían tener ZIP")

if __name__ == "__main__":
    main()
'''
    
    with open('analyze_real_emails.py', 'w', encoding='utf-8') as f:
        f.write(analyzer_script)
    
    print("✅ Analizador creado: analyze_real_emails.py")

def restart_mailbox_service():
    """Reiniciar solo el mailbox service para aplicar cambios"""
    print("\n=== REINICIANDO MAILBOX SERVICE ===")
    
    try:
        # Verificar si está corriendo
        response = requests.get('http://localhost:8001/health', timeout=3)
        if response.status_code == 200:
            print("✅ Mailbox service está corriendo")
            print("⚠️  Necesitas reiniciarlo manualmente para aplicar los cambios de logging")
            print("   1. Detener: Ctrl+C en la ventana donde corre start_services_fixed.py")
            print("   2. Reiniciar: python start_services_fixed.py")
            return True
        else:
            print("❌ Mailbox service no responde correctamente")
            return False
    except:
        print("❌ Mailbox service no está corriendo")
        print("   Ejecutar: python start_services_fixed.py")
        return False

def main():
    """Función principal"""
    print("=" * 60)
    print("DEBUG DE EMAILS REALES - DETECCIÓN DE ZIP")
    print("=" * 60)
    
    print("🎯 OBJETIVO:")
    print("   Entender por qué los emails reales no detectan archivos ZIP")
    print("   aunque la lógica funciona perfectamente en simulación")
    
    # 1. Habilitar logging detallado
    logging_enabled = enable_detailed_logging()
    
    # 2. Agregar logging de contenido
    content_logging = add_email_content_logging()
    
    # 3. Crear analizador
    create_real_email_analyzer()
    
    # 4. Instrucciones para reiniciar
    restart_mailbox_service()
    
    print(f"\n{'='*60}")
    print("MODIFICACIONES COMPLETADAS")
    print(f"{'='*60}")
    
    if logging_enabled and content_logging:
        print("\n✅ LOGGING DETALLADO HABILITADO")
        print("\n📋 PRÓXIMOS PASOS:")
        print("1. Reiniciar el mailbox service:")
        print("   - Detener: Ctrl+C en start_services_fixed.py")
        print("   - Reiniciar: python start_services_fixed.py")
        print("")
        print("2. Ejecutar análisis de emails reales:")
        print("   python analyze_real_emails.py")
        print("")
        print("3. Revisar logs detallados:")
        print("   Los logs mostrarán exactamente qué ve el sistema en cada email")
        print("   Buscar líneas que empiecen con 'DEBUGGING -'")
        print("")
        print("4. Comparar con formato esperado:")
        print("   Identificar diferencias entre emails reales y simulados")
        
        print(f"\n💡 ESTO REVELARÁ:")
        print("   - Formato exacto de los emails reales")
        print("   - Por qué no se detectan como ZIP")
        print("   - Qué ajustes necesita la lógica de detección")
    else:
        print("\n❌ HUBO PROBLEMAS EN LAS MODIFICACIONES")
        print("   Revisar manualmente el archivo email_service.py")

if __name__ == "__main__":
    main()
