#!/usr/bin/env python3
"""
Script corregido para iniciar todos los servicios con las importaciones correctas
"""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path

def start_service_with_correct_path(service_name, service_dir, port, app_module="main:app"):
    """Iniciar un servicio con el PYTHONPATH correcto"""
    
    print(f"Iniciando {service_name} en puerto {port}...")
    
    # Configurar el entorno
    env = os.environ.copy()
    
    # Agregar el directorio raíz al PYTHONPATH
    project_root = Path(__file__).parent.absolute()
    if 'PYTHONPATH' in env:
        env['PYTHONPATH'] = f"{project_root};{env['PYTHONPATH']}"
    else:
        env['PYTHONPATH'] = str(project_root)
    
    # Comando para iniciar el servicio
    cmd = [
        sys.executable, "-m", "uvicorn",
        app_module,
        "--host", "0.0.0.0",
        "--port", str(port),
        "--reload"
    ]
    
    try:
        # Cambiar al directorio del servicio
        service_path = project_root / service_dir
        
        # Iniciar el proceso
        process = subprocess.Popen(
            cmd,
            cwd=service_path,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ {service_name} iniciado (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"❌ Error iniciando {service_name}: {e}")
        return None

def check_service_health(port, service_name, max_retries=10):
    """Verificar que un servicio esté respondiendo"""
    import requests
    
    for i in range(max_retries):
        try:
            response = requests.get(f"http://localhost:{port}/health", timeout=2)
            if response.status_code == 200:
                print(f"✅ {service_name} está respondiendo correctamente")
                return True
        except:
            pass
        
        if i < max_retries - 1:
            print(f"⏳ Esperando {service_name}... ({i+1}/{max_retries})")
            time.sleep(2)
    
    print(f"❌ {service_name} no está respondiendo después de {max_retries} intentos")
    return False

def main():
    """Función principal"""
    print("=" * 60)
    print("INICIANDO SERVICIOS CON IMPORTACIONES CORREGIDAS")
    print("=" * 60)
    
    # Configuración de servicios
    services = [
        ("API Service", "services/api_service", 8000),
        ("Mailbox Service", "services/mailbox_service", 8001),
        ("File Processing Service", "services/file_processing_service", 8002),
        ("Extraction Service", "services/extraction_service", 8003)
    ]
    
    processes = []
    
    # Iniciar todos los servicios
    for service_name, service_dir, port in services:
        process = start_service_with_correct_path(service_name, service_dir, port)
        if process:
            processes.append((process, service_name, port))
        time.sleep(2)  # Esperar entre servicios
    
    if not processes:
        print("❌ No se pudo iniciar ningún servicio")
        return
    
    print(f"\n⏳ Esperando que los servicios se inicialicen...")
    time.sleep(10)
    
    # Verificar salud de los servicios
    print(f"\n🔍 VERIFICANDO SALUD DE LOS SERVICIOS:")
    healthy_services = 0
    
    for process, service_name, port in processes:
        if process.poll() is None:  # Proceso aún corriendo
            if check_service_health(port, service_name):
                healthy_services += 1
        else:
            print(f"❌ {service_name} se detuvo inesperadamente")
            # Mostrar error
            stdout, stderr = process.communicate()
            if stderr:
                print(f"   Error: {stderr[:200]}...")
    
    print(f"\n📊 RESUMEN:")
    print(f"   Servicios iniciados: {len(processes)}")
    print(f"   Servicios saludables: {healthy_services}")
    
    if healthy_services == len(services):
        print(f"\n🎉 ¡TODOS LOS SERVICIOS ESTÁN FUNCIONANDO CORRECTAMENTE!")
        print(f"\n🔧 PRÓXIMOS PASOS:")
        print("1. Configurar credenciales de email reales")
        print("2. Ejecutar: python test_zip_detection_real.py")
        print("3. Revisar logs para verificar detección de ZIP")
        
        print(f"\n⚠️  PARA DETENER LOS SERVICIOS:")
        print("   Presiona Ctrl+C en esta ventana")
        
        try:
            # Mantener el script corriendo
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n🛑 Deteniendo servicios...")
            for process, service_name, port in processes:
                if process.poll() is None:
                    print(f"   Deteniendo {service_name}...")
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
            print("✅ Todos los servicios detenidos")
    
    else:
        print(f"\n❌ ALGUNOS SERVICIOS TIENEN PROBLEMAS")
        print("   Revisa los errores mostrados arriba")
        print("   Puede ser necesario instalar dependencias faltantes")
        
        # Detener procesos problemáticos
        for process, service_name, port in processes:
            if process.poll() is None:
                process.terminate()

if __name__ == "__main__":
    main()
