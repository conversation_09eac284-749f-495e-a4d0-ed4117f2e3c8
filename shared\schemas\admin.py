"""
Pydantic schemas for admin operations
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

# User Management Schemas

class UserStatus(str, Enum):
    """User status enumeration"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    INACTIVE = "inactive"

class AdminUserCreate(BaseModel):
    """Schema for admin creating a new user"""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    client_id: int
    is_admin: bool = False
    is_active: bool = True

    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        return v

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('Password must be at least 6 characters long')
        return v

class AdminUserUpdate(BaseModel):
    """Schema for admin updating user information"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    client_id: Optional[int] = None
    is_admin: Optional[bool] = None
    is_active: Optional[bool] = None

class AdminUserResponse(BaseModel):
    """Schema for user information in admin responses"""
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    client_id: int
    client_name: Optional[str] = None
    is_admin: bool
    is_active: bool
    created_date: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class UserListResponse(BaseModel):
    """Schema for paginated user list response"""
    users: List[AdminUserResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool

class UserSuspendRequest(BaseModel):
    """Schema for user suspension request"""
    reason: Optional[str] = None

# Cost Tracking Schemas

class LLMUsageResponse(BaseModel):
    """Schema for LLM usage record response"""
    id: int
    user_id: int
    username: Optional[str] = None
    client_id: int
    client_name: Optional[str] = None
    provider: str
    model_name: str
    input_tokens: int
    output_tokens: int
    total_tokens: int
    input_cost: str
    output_cost: str
    total_cost: str
    request_type: Optional[str] = None
    processing_time_ms: Optional[int] = None
    success: bool
    error_message: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class DeploymentCostResponse(BaseModel):
    """Schema for deployment cost record response"""
    id: int
    service_name: str
    cost_type: str
    cost_amount: str
    currency: str
    period_start: datetime
    period_end: datetime
    billing_period: str
    resource_usage: Optional[Dict[str, Any]] = None
    resource_units: Optional[str] = None
    provider: Optional[str] = None
    region: Optional[str] = None
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class CostSummary(BaseModel):
    """Schema for cost summary statistics"""
    total_cost: str
    period_start: datetime
    period_end: datetime
    currency: str = "USD"

class LLMCostSummary(CostSummary):
    """Schema for LLM cost summary"""
    total_requests: int
    total_tokens: int
    successful_requests: int
    failed_requests: int
    top_models: List[Dict[str, Any]]
    top_users: List[Dict[str, Any]]

class DeploymentCostSummary(CostSummary):
    """Schema for deployment cost summary"""
    total_services: int
    cost_by_service: List[Dict[str, Any]]
    cost_by_type: List[Dict[str, Any]]

# Dashboard Schemas

class AdminDashboardStats(BaseModel):
    """Schema for admin dashboard statistics"""
    total_users: int
    active_users: int
    suspended_users: int
    total_clients: int
    active_clients: int
    
    # LLM Usage Stats
    llm_requests_today: int
    llm_requests_this_month: int
    llm_cost_today: str
    llm_cost_this_month: str
    
    # System Stats
    total_invoices_processed: int
    invoices_processed_today: int
    invoices_processed_this_month: int
    
    # Recent Activity
    recent_users: List[AdminUserResponse]
    recent_llm_usage: List[LLMUsageResponse]

class DateRangeFilter(BaseModel):
    """Schema for date range filtering"""
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

class LLMUsageFilter(DateRangeFilter):
    """Schema for LLM usage filtering"""
    user_id: Optional[int] = None
    client_id: Optional[int] = None
    provider: Optional[str] = None
    model_name: Optional[str] = None
    success: Optional[bool] = None

class DeploymentCostFilter(DateRangeFilter):
    """Schema for deployment cost filtering"""
    service_name: Optional[str] = None
    cost_type: Optional[str] = None
    provider: Optional[str] = None

class PaginationParams(BaseModel):
    """Schema for pagination parameters"""
    page: int = 1
    per_page: int = 20
    
    @validator('page')
    def validate_page(cls, v):
        if v < 1:
            raise ValueError('Page must be greater than 0')
        return v
    
    @validator('per_page')
    def validate_per_page(cls, v):
        if v < 1 or v > 100:
            raise ValueError('Per page must be between 1 and 100')
        return v

# Response Schemas

class AdminResponse(BaseModel):
    """Generic admin response schema"""
    success: bool
    message: str
    data: Optional[Any] = None

class ErrorResponse(BaseModel):
    """Error response schema"""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
