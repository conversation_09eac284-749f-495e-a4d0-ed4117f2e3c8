#!/usr/bin/env python3
"""
Script to run database migration and test the enhanced functionality
"""

import os
import sys
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_migration():
    """Run the database migration"""
    print("Running database migration...")
    
    try:
        from scripts.migrate_enhanced_invoice_fields import main as migrate_main
        migrate_main()
        print("✅ Migration completed successfully")
        return True
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        return False

def test_database_connection():
    """Test database connection"""
    print("Testing database connection...")
    
    try:
        from shared.database.connection import get_db
        from shared.database.models import CUFERecord
        from sqlalchemy.orm import Session
        
        # Get database session
        db_gen = get_db()
        db: Session = next(db_gen)
        
        # Try to query the database
        count = db.query(CUFERecord).count()
        print(f"✅ Database connection successful. Found {count} CUFE records.")
        
        # Check if new columns exist
        from sqlalchemy import inspect
        inspector = inspect(db.bind)
        columns = inspector.get_columns('cufe_records')
        column_names = [col['name'] for col in columns]
        
        new_columns = [
            'tax_exclusive_amount', 'tax_inclusive_amount', 'total_tax_amount',
            'iva_amount', 'rete_fuente_amount', 'currency_code'
        ]
        
        print("Checking for new columns:")
        for col in new_columns:
            if col in column_names:
                print(f"  ✅ {col} - exists")
            else:
                print(f"  ❌ {col} - missing")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def test_api_response():
    """Test API response format"""
    print("Testing API response format...")
    
    try:
        from shared.schemas.api import CUFEResponse
        from shared.schemas.extraction import InvoiceLineItemData
        
        # Test creating a CUFEResponse with new fields
        response = CUFEResponse(
            cufe_value="test-cufe",
            email_id="test-email",
            reception_date="2025-09-06T00:00:00",
            xml_file_path="/test/path.xml",
            processed_date="2025-09-06T00:00:00",
            tax_exclusive_amount="100000.00",
            iva_amount="19000.00",
            line_items=[
                InvoiceLineItemData(
                    line_number=1,
                    item_name="Test Item",
                    unit_price="100000.00"
                )
            ]
        )
        
        print("✅ API response schema validation successful")
        print(f"  - CUFE: {response.cufe_value}")
        print(f"  - Tax Exclusive: {response.tax_exclusive_amount}")
        print(f"  - IVA: {response.iva_amount}")
        print(f"  - Line Items: {len(response.line_items) if response.line_items else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ API response test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("Enhanced CUFE System Migration and Test")
    print("=" * 50)
    
    # Wait a moment for Docker containers to be ready
    print("Waiting for services to be ready...")
    time.sleep(5)
    
    success = True
    
    # Run migration
    if not run_migration():
        success = False
    
    print()
    
    # Test database
    if not test_database_connection():
        success = False
    
    print()
    
    # Test API schema
    if not test_api_response():
        success = False
    
    print()
    print("=" * 50)
    if success:
        print("🎉 All tests passed! Enhanced CUFE system is ready.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
