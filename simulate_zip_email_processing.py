#!/usr/bin/env python3
"""
Script para simular el procesamiento de emails con archivos ZIP
usando los archivos ZIP existentes en temp_files
"""

import os
import sys
import email
import zipfile
import uuid
from pathlib import Path
from datetime import datetime
from email.mime.multipart import MIMEM<PERSON>ipart
from email.mime.application import MIMEApplication
from email.mime.text import MIMEText

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_fake_email_with_zip(zip_file_path, sender="<EMAIL>", subject="Factura Electrónica"):
    """Crear un email falso con un archivo ZIP adjunto"""
    
    # Crear mensaje multipart
    msg = MIMEMultipart()
    msg['From'] = sender
    msg['To'] = "<EMAIL>"
    msg['Subject'] = subject
    msg['Date'] = email.utils.formatdate(localtime=True)
    
    # Agregar cuerpo del mensaje
    body = "Adjunto encontrará la factura electrónica solicitada."
    msg.attach(MIMEText(body, 'plain'))
    
    # Leer el archivo ZIP
    with open(zip_file_path, 'rb') as f:
        zip_data = f.read()
    
    # Crear adjunto ZIP
    zip_attachment = MIMEApplication(zip_data, _subtype='zip')
    zip_attachment.add_header(
        'Content-Disposition', 
        'attachment', 
        filename=zip_file_path.name
    )
    
    msg.attach(zip_attachment)
    
    return msg

def test_zip_detection_logic(email_message):
    """Probar la lógica de detección de ZIP en un mensaje de email"""
    
    print("🔍 PROBANDO DETECCIÓN DE ZIP...")
    
    # Simular la lógica de detección que usa el sistema
    zip_attachments = []
    attachment_count = 0
    
    for part in email_message.walk():
        if part.get_content_disposition() == 'attachment':
            attachment_count += 1
            filename = part.get_filename()
            content_type = part.get_content_type()
            
            print(f"   📎 Adjunto encontrado:")
            print(f"      Nombre: {filename}")
            print(f"      Tipo: {content_type}")
            print(f"      Disposición: {part.get_content_disposition()}")
            
            if filename and filename.lower().endswith('.zip'):
                zip_attachments.append({
                    'filename': filename,
                    'part': part,
                    'content_type': content_type
                })
                print(f"      ✅ DETECTADO COMO ZIP")
            else:
                print(f"      ❌ No detectado como ZIP")
    
    has_zip = len(zip_attachments) > 0
    
    print(f"\n📊 RESULTADO DE DETECCIÓN:")
    print(f"   Total adjuntos: {attachment_count}")
    print(f"   Adjuntos ZIP: {len(zip_attachments)}")
    print(f"   Tiene ZIP: {has_zip}")
    
    return has_zip, zip_attachments

def test_bodystructure_detection(email_message):
    """Probar detección usando bodystructure (como hace el sistema real)"""
    
    print("\n🔍 PROBANDO DETECCIÓN POR BODYSTRUCTURE...")
    
    # Convertir mensaje a string para simular bodystructure
    email_str = email_message.as_string().lower()
    
    # Indicadores que busca el sistema
    zip_indicators = [
        'application/zip',
        'application/x-zip',
        'application/x-zip-compressed',
        'application/octet-stream',
        'application/x-compressed',
        'multipart/x-zip',
    ]
    
    zip_filename_patterns = [
        '.zip',
        'filename=',
        'name=',
        'attachment',
        'content-disposition',
        'factura',
        'invoice',
        'documento',
    ]
    
    found_indicators = []
    found_patterns = []
    
    for indicator in zip_indicators:
        if indicator in email_str:
            found_indicators.append(indicator)
    
    for pattern in zip_filename_patterns:
        if pattern in email_str:
            found_patterns.append(pattern)
    
    has_zip_content = len(found_indicators) > 0
    has_zip_filename = '.zip' in email_str and len(found_patterns) > 1
    has_zip = has_zip_content or has_zip_filename
    
    print(f"   Indicadores de contenido encontrados: {found_indicators}")
    print(f"   Patrones de filename encontrados: {found_patterns}")
    print(f"   Detección por contenido: {has_zip_content}")
    print(f"   Detección por filename: {has_zip_filename}")
    print(f"   RESULTADO FINAL: {has_zip}")
    
    return has_zip

def simulate_email_processing():
    """Simular el procesamiento completo de emails con ZIP"""
    
    print("=" * 60)
    print("SIMULACIÓN DE PROCESAMIENTO DE EMAILS CON ZIP")
    print("=" * 60)
    
    # Buscar archivos ZIP en temp_files
    temp_files_path = Path('temp_files')
    if not temp_files_path.exists():
        print("❌ No existe directorio temp_files")
        return
    
    zip_files = list(temp_files_path.glob('*.zip'))[:5]  # Probar con 5 archivos
    
    if not zip_files:
        print("❌ No hay archivos ZIP para probar")
        return
    
    print(f"📦 Probando con {len(zip_files)} archivos ZIP...")
    
    total_detected = 0
    
    for i, zip_file in enumerate(zip_files, 1):
        print(f"\n{'='*40}")
        print(f"PRUEBA {i}/{len(zip_files)}: {zip_file.name}")
        print(f"{'='*40}")
        
        try:
            # Crear email falso con ZIP
            fake_email = create_fake_email_with_zip(
                zip_file, 
                sender=f"sender{i}@company.com",
                subject=f"Factura #{1000+i} - {zip_file.stem}"
            )
            
            # Probar detección por adjuntos (método actual)
            has_zip_attachments, zip_attachments = test_zip_detection_logic(fake_email)
            
            # Probar detección por bodystructure (método del sistema)
            has_zip_bodystructure = test_bodystructure_detection(fake_email)
            
            # Verificar consistencia
            if has_zip_attachments and has_zip_bodystructure:
                print(f"\n✅ DETECCIÓN EXITOSA - Ambos métodos detectaron ZIP")
                total_detected += 1
            elif has_zip_attachments and not has_zip_bodystructure:
                print(f"\n⚠️  INCONSISTENCIA - Solo método de adjuntos detectó ZIP")
                print("   El sistema real podría fallar en la detección")
            elif not has_zip_attachments and has_zip_bodystructure:
                print(f"\n⚠️  INCONSISTENCIA - Solo bodystructure detectó ZIP")
                print("   Problema en lógica de adjuntos")
            else:
                print(f"\n❌ FALLO TOTAL - Ningún método detectó ZIP")
            
            # Si se detectó, simular descarga
            if has_zip_attachments:
                print(f"\n📥 SIMULANDO DESCARGA...")
                for attachment in zip_attachments:
                    print(f"   Descargando: {attachment['filename']}")
                    print(f"   Tipo: {attachment['content_type']}")
                    
                    # Verificar que el ZIP es válido
                    try:
                        zip_data = attachment['part'].get_payload(decode=True)
                        # Intentar abrir como ZIP
                        import io
                        with zipfile.ZipFile(io.BytesIO(zip_data), 'r') as zf:
                            files_in_zip = zf.namelist()
                            print(f"   ✅ ZIP válido con {len(files_in_zip)} archivos")
                    except Exception as e:
                        print(f"   ❌ Error validando ZIP: {e}")
            
        except Exception as e:
            print(f"❌ Error procesando {zip_file.name}: {e}")
    
    # Resumen
    print(f"\n{'='*60}")
    print("RESUMEN DE LA SIMULACIÓN")
    print(f"{'='*60}")
    print(f"📦 Archivos ZIP probados: {len(zip_files)}")
    print(f"✅ Detecciones exitosas: {total_detected}")
    print(f"❌ Detecciones fallidas: {len(zip_files) - total_detected}")
    
    if total_detected == len(zip_files):
        print(f"\n🎉 ¡PERFECTO! Todos los archivos ZIP fueron detectados correctamente")
        print("El problema puede estar en la configuración o en el procesamiento real de emails")
    elif total_detected > 0:
        print(f"\n⚠️  DETECCIÓN PARCIAL: {total_detected}/{len(zip_files)} archivos detectados")
        print("Hay problemas en la lógica de detección que necesitan corrección")
    else:
        print(f"\n❌ FALLO COMPLETO: No se detectó ningún archivo ZIP")
        print("La lógica de detección tiene problemas serios")
    
    print(f"\n💡 RECOMENDACIONES:")
    if total_detected < len(zip_files):
        print("1. Revisar la lógica de detección en email_service.py")
        print("2. Verificar que LOG_ZIP_DETECTION=true esté configurado")
        print("3. Reiniciar el mailbox service para aplicar cambios")
        print("4. Probar con emails reales usando credenciales válidas")
    else:
        print("1. La detección funciona correctamente en simulación")
        print("2. El problema puede estar en:")
        print("   - Configuración de credenciales de email")
        print("   - Filtros de fecha demasiado restrictivos")
        print("   - Emails reales sin archivos ZIP")
        print("   - Problemas de conectividad IMAP")

def main():
    """Función principal"""
    simulate_email_processing()

if __name__ == "__main__":
    main()
