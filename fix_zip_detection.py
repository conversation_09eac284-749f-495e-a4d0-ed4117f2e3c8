#!/usr/bin/env python3
"""
Script para diagnosticar y corregir la detección de archivos ZIP en correos electrónicos
"""

import os
import sys
import requests
import json
from pathlib import Path

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def enable_zip_detection_logging():
    """Habilitar logging detallado para detección de ZIP modificando el código"""
    print("=== HABILITANDO LOGGING DE DETECCIÓN ZIP ===")
    
    # Modificar el archivo de configuración del email service
    email_service_path = Path('services/mailbox_service/email_service.py')
    
    if not email_service_path.exists():
        print("❌ No se encuentra el archivo email_service.py")
        return False
    
    # Leer el archivo
    with open(email_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Buscar la línea donde se inicializa la configuración
    if "self.config = {" in content:
        # Reemplazar la configuración para habilitar logging
        old_config = """self.config = {
            'zip_filter_batch_size': 50,
            'max_non_zip_emails': 100
        }"""
        
        new_config = """self.config = {
            'zip_filter_batch_size': 50,
            'max_non_zip_emails': 100,
            'log_zip_detection': True  # Habilitar logging detallado
        }"""
        
        if old_config in content:
            content = content.replace(old_config, new_config)
            
            # Escribir el archivo modificado
            with open(email_service_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Logging de detección ZIP habilitado")
            return True
        else:
            print("⚠️  Configuración no encontrada en el formato esperado")
            return False
    else:
        print("⚠️  No se encontró la configuración en email_service.py")
        return False

def improve_zip_detection():
    """Mejorar la lógica de detección de archivos ZIP"""
    print("\n=== MEJORANDO DETECCIÓN DE ARCHIVOS ZIP ===")
    
    email_service_path = Path('services/mailbox_service/email_service.py')
    
    if not email_service_path.exists():
        print("❌ No se encuentra el archivo email_service.py")
        return False
    
    # Leer el archivo
    with open(email_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Buscar la función _has_zip_in_bodystructure y mejorarla
    old_zip_indicators = """        zip_indicators = [
            'application/zip',
            'application/x-zip',
            'application/x-zip-compressed',
            'application/octet-stream',  # ZIP files are sometimes sent as octet-stream
        ]"""
    
    new_zip_indicators = """        zip_indicators = [
            'application/zip',
            'application/x-zip',
            'application/x-zip-compressed',
            'application/octet-stream',  # ZIP files are sometimes sent as octet-stream
            'application/x-compressed',
            'multipart/x-zip',
            'application/x-zip-compressed',
            'application/pkcs7-mime',  # Sometimes used for ZIP
            'application/vnd.ms-office',  # Sometimes ZIP files are detected as this
        ]"""
    
    if old_zip_indicators in content:
        content = content.replace(old_zip_indicators, new_zip_indicators)
        print("✅ Indicadores ZIP ampliados")
    
    # Mejorar también los patrones de filename
    old_filename_patterns = """        zip_filename_patterns = [
            '.zip',
            'filename=',
            'name=',
        ]"""
    
    new_filename_patterns = """        zip_filename_patterns = [
            '.zip',
            'filename=',
            'name=',
            'attachment',
            'Content-Disposition',
            'factura',  # Común en facturas
            'invoice',  # Común en facturas
            'documento',  # Común en documentos
        ]"""
    
    if old_filename_patterns in content:
        content = content.replace(old_filename_patterns, new_filename_patterns)
        print("✅ Patrones de filename ampliados")
    
    # Escribir el archivo modificado
    with open(email_service_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Detección de ZIP mejorada")
    return True

def add_fallback_zip_detection():
    """Agregar método de detección alternativo más agresivo"""
    print("\n=== AGREGANDO DETECCIÓN ALTERNATIVA ===")
    
    email_service_path = Path('services/mailbox_service/email_service.py')
    
    # Leer el archivo
    with open(email_service_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Buscar donde agregar el método alternativo
    # Lo agregaremos después de _confirm_zip_attachment
    insertion_point = "        except Exception as e:\n            logger.warning(f\"Failed to confirm ZIP attachment for email {email_id}: {str(e)}\")\n            return False"
    
    if insertion_point in content:
        # Nuevo método de detección más agresivo
        new_method = """
    def _aggressive_zip_detection(self, mail: imaplib.IMAP4_SSL, email_id: str) -> bool:
        \"\"\"
        Método más agresivo para detectar archivos ZIP
        \"\"\"
        try:
            # Obtener el mensaje completo para análisis detallado
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            
            if status != 'OK' or not msg_data:
                return False
            
            # Convertir a string para búsqueda
            full_message = msg_data[0][1]
            if isinstance(full_message, bytes):
                full_message = full_message.decode('utf-8', errors='ignore')
            
            message_lower = full_message.lower()
            
            # Buscar indicadores más amplios
            zip_indicators = [
                'application/zip',
                'application/octet-stream',
                '.zip',
                'filename=',
                'attachment',
                'content-disposition',
                'factura',
                'invoice',
                'documento'
            ]
            
            found_indicators = []
            for indicator in zip_indicators:
                if indicator in message_lower:
                    found_indicators.append(indicator)
            
            # Log lo que encontramos
            if found_indicators:
                logger.info(f\"Email {email_id}: Aggressive detection found: {found_indicators}\")
                return True
            else:
                logger.info(f\"Email {email_id}: No ZIP indicators found in aggressive detection\")
                return False
                
        except Exception as e:
            logger.warning(f\"Aggressive ZIP detection failed for email {email_id}: {str(e)}\")
            return False"""
        
        # Insertar el nuevo método
        content = content.replace(insertion_point, insertion_point + new_method)
        
        # Ahora modificar _check_batch_for_zip_attachments para usar el método agresivo
        old_check = """                            if has_zip_in_body:
                                # Do a more detailed check to confirm
                                confirmed = self._confirm_zip_attachment(mail, email_id_str)
                                if self.config.get('log_zip_detection', False):
                                    logger.info(f\"Email {email_id_for_logging}: ZIP confirmed = {confirmed}\")

                                if confirmed:
                                    zip_email_ids.append(email_id)  # Keep original format
                                    logger.info(f\"Email {email_id_for_logging} has ZIP attachment\")"""
        
        new_check = """                            if has_zip_in_body:
                                # Do a more detailed check to confirm
                                confirmed = self._confirm_zip_attachment(mail, email_id_str)
                                if self.config.get('log_zip_detection', False):
                                    logger.info(f\"Email {email_id_for_logging}: ZIP confirmed = {confirmed}\")

                                # Si no se confirma, probar método agresivo
                                if not confirmed:
                                    confirmed = self._aggressive_zip_detection(mail, email_id_str)
                                    if self.config.get('log_zip_detection', False):
                                        logger.info(f\"Email {email_id_for_logging}: Aggressive ZIP detection = {confirmed}\")

                                if confirmed:
                                    zip_email_ids.append(email_id)  # Keep original format
                                    logger.info(f\"Email {email_id_for_logging} has ZIP attachment\")"""
        
        if old_check in content:
            content = content.replace(old_check, new_check)
            print("✅ Detección agresiva integrada")
        
        # Escribir el archivo modificado
        with open(email_service_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Método de detección alternativo agregado")
        return True
    else:
        print("⚠️  No se pudo encontrar el punto de inserción")
        return False

def create_test_email_processor():
    """Crear un procesador de prueba para emails específicos"""
    print("\n=== CREANDO PROCESADOR DE PRUEBA ===")
    
    test_script = '''#!/usr/bin/env python3
"""
Script de prueba para procesar emails específicos con logging detallado
"""

import requests
import json

def test_email_processing():
    """Probar procesamiento de emails con configuración de prueba"""
    
    # Configuración de prueba - AJUSTAR CON CREDENCIALES REALES
    test_request = {
        "email_host": "imap.gmail.com",
        "email_port": 993,
        "email_username": "<EMAIL>",  # CAMBIAR
        "email_password": "tu_password",         # CAMBIAR
        "use_ssl": True,
        "folder": "INBOX",
        "date_from": "2025-09-01",
        "date_to": "2025-09-17",
        "max_emails": 5,  # Procesar solo 5 emails para prueba
        "client_id": 1,
        "zip_only": False  # Procesar todos los emails para ver qué pasa
    }
    
    print("🧪 INICIANDO PRUEBA DE PROCESAMIENTO DE EMAILS")
    print("⚠️  IMPORTANTE: Configura credenciales reales en el script")
    
    try:
        response = requests.post(
            'http://localhost:8001/process-emails',
            json=test_request,
            timeout=120
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Procesamiento exitoso:")
            print(f"   Emails procesados: {result.get('processed_count', 0)}")
            print(f"   Emails con ZIP: {result.get('emails_with_zip_attachments', 0)}")
            print(f"   Archivos descargados: {len(result.get('downloaded_files', []))}")
            
            # Mostrar muestra de senders y subjects
            sample_senders = result.get('sample_email_senders', [])
            sample_subjects = result.get('sample_email_subjects', [])
            
            print("\\n📧 Muestra de emails procesados:")
            for i, (sender, subject) in enumerate(zip(sample_senders, sample_subjects)):
                print(f"   {i+1}. De: {sender}")
                print(f"      Asunto: {subject}")
        else:
            print(f"❌ Error en procesamiento: {response.status_code}")
            print(f"   Respuesta: {response.text}")
            
    except Exception as e:
        print(f"❌ Error conectando al servicio: {e}")

if __name__ == "__main__":
    test_email_processing()
'''
    
    with open('test_email_processing.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ Script de prueba creado: test_email_processing.py")
    print("⚠️  Recuerda configurar credenciales reales antes de ejecutarlo")

def main():
    """Función principal"""
    print("=" * 60)
    print("CORRECCIÓN DE DETECCIÓN DE ARCHIVOS ZIP EN EMAILS")
    print("=" * 60)
    
    # 1. Habilitar logging detallado
    enable_zip_detection_logging()
    
    # 2. Mejorar detección de ZIP
    improve_zip_detection()
    
    # 3. Agregar método alternativo
    add_fallback_zip_detection()
    
    # 4. Crear script de prueba
    create_test_email_processor()
    
    print(f"\n{'=' * 60}")
    print("CORRECCIÓN COMPLETADA")
    print(f"{'=' * 60}")
    
    print("\n📋 PRÓXIMOS PASOS:")
    print("1. Reiniciar el mailbox service para aplicar los cambios")
    print("2. Configurar credenciales reales en test_email_processing.py")
    print("3. Ejecutar el script de prueba para verificar la detección")
    print("4. Revisar los logs detallados para diagnosticar problemas")
    
    print("\n🔧 COMANDOS:")
    print("   # Reiniciar servicio:")
    print("   cd services/mailbox_service")
    print("   python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload")
    print("")
    print("   # Probar detección:")
    print("   python test_email_processing.py")

if __name__ == "__main__":
    main()
