#!/usr/bin/env python3
"""
Script de prueba para procesar emails específicos con logging detallado
"""

import requests
import json

def test_email_processing():
    """Probar procesamiento de emails con configuración de prueba"""
    
    # Configuración de prueba - AJUSTAR CON CREDENCIALES REALES
    test_request = {
        "email_host": "imap.gmail.com",
        "email_port": 993,
        "email_username": "<EMAIL>",  # CAMBIAR
        "email_password": "tu_password",         # CAMBIAR
        "use_ssl": True,
        "folder": "INBOX",
        "date_from": "2025-09-01",
        "date_to": "2025-09-17",
        "max_emails": 5,  # Procesar solo 5 emails para prueba
        "client_id": 1,
        "zip_only": False  # Procesar todos los emails para ver qué pasa
    }
    
    print("🧪 INICIANDO PRUEBA DE PROCESAMIENTO DE EMAILS")
    print("⚠️  IMPORTANTE: Configura credenciales reales en el script")
    
    try:
        response = requests.post(
            'http://localhost:8001/process-emails',
            json=test_request,
            timeout=120
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Procesamiento exitoso:")
            print(f"   Emails procesados: {result.get('processed_count', 0)}")
            print(f"   Emails con ZIP: {result.get('emails_with_zip_attachments', 0)}")
            print(f"   Archivos descargados: {len(result.get('downloaded_files', []))}")
            
            # Mostrar muestra de senders y subjects
            sample_senders = result.get('sample_email_senders', [])
            sample_subjects = result.get('sample_email_subjects', [])
            
            print("\n📧 Muestra de emails procesados:")
            for i, (sender, subject) in enumerate(zip(sample_senders, sample_subjects)):
                print(f"   {i+1}. De: {sender}")
                print(f"      Asunto: {subject}")
        else:
            print(f"❌ Error en procesamiento: {response.status_code}")
            print(f"   Respuesta: {response.text}")
            
    except Exception as e:
        print(f"❌ Error conectando al servicio: {e}")

if __name__ == "__main__":
    test_email_processing()
