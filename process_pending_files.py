#!/usr/bin/env python3
"""
Script para procesar todos los archivos ZIP pendientes
"""

import os
import sys
import requests
import time
from pathlib import Path
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_services():
    """Verificar que todos los servicios estén activos"""
    services = [
        ('API Service', 'http://localhost:8000/health'),
        ('Mailbox Service', 'http://localhost:8001/health'),
        ('File Processing Service', 'http://localhost:8002/health'),
        ('Extraction Service', 'http://localhost:8003/health')
    ]
    
    print("=== VERIFICANDO SERVICIOS ===")
    all_active = True
    
    for service_name, health_url in services:
        try:
            response = requests.get(health_url, timeout=5)
            if response.status_code == 200:
                print(f"✓ {service_name}: Activo")
            else:
                print(f"✗ {service_name}: Error {response.status_code}")
                all_active = False
        except Exception as e:
            print(f"✗ {service_name}: No disponible - {e}")
            all_active = False
    
    return all_active

def process_zip_file(zip_file_path):
    """Procesar un archivo ZIP específico"""
    try:
        # 1. Procesar el archivo ZIP
        process_response = requests.post(
            'http://localhost:8002/process-zip',
            json={
                'file_path': str(zip_file_path),
                'preserve_structure': True
            },
            timeout=60
        )
        
        if process_response.status_code != 200:
            return False, f"Error procesando ZIP: {process_response.text}"
        
        process_result = process_response.json()
        xml_files = process_result.get('xml_files', [])
        
        if not xml_files:
            return True, "ZIP procesado pero no contiene archivos XML"
        
        # 2. Extraer CUFE de cada archivo XML
        cufe_results = []
        for xml_file in xml_files:
            try:
                extraction_response = requests.post(
                    'http://localhost:8003/extract-cufe',
                    json={
                        'xml_file_path': xml_file['file_path'],
                        'extract_additional_data': True
                    },
                    timeout=30
                )
                
                if extraction_response.status_code == 200:
                    extraction_result = extraction_response.json()
                    if extraction_result.get('success'):
                        cufe_results.append(extraction_result.get('cufe_value', 'N/A'))
                    else:
                        cufe_results.append(f"Error: {extraction_result.get('message', 'Unknown')}")
                else:
                    cufe_results.append(f"HTTP Error: {extraction_response.status_code}")
                    
            except Exception as e:
                cufe_results.append(f"Exception: {str(e)}")
        
        return True, f"Procesado: {len(xml_files)} XML, CUFEs: {cufe_results}"
        
    except Exception as e:
        return False, f"Error general: {str(e)}"

def process_all_pending_files():
    """Procesar todos los archivos ZIP pendientes"""
    print("\n=== PROCESANDO ARCHIVOS ZIP PENDIENTES ===")
    
    temp_files_path = Path('temp_files')
    if not temp_files_path.exists():
        print("No existe el directorio temp_files")
        return
    
    zip_files = list(temp_files_path.glob('*.zip'))
    print(f"Archivos ZIP encontrados: {len(zip_files)}")
    
    if not zip_files:
        print("No hay archivos ZIP para procesar")
        return
    
    processed_count = 0
    failed_count = 0
    
    for i, zip_file in enumerate(zip_files, 1):
        print(f"\n[{i}/{len(zip_files)}] Procesando: {zip_file.name}")
        
        success, message = process_zip_file(zip_file)
        
        if success:
            print(f"  ✓ {message}")
            processed_count += 1
        else:
            print(f"  ✗ {message}")
            failed_count += 1
        
        # Pausa pequeña entre archivos para no sobrecargar
        time.sleep(0.5)
    
    print(f"\n=== RESUMEN ===")
    print(f"Total archivos: {len(zip_files)}")
    print(f"Procesados exitosamente: {processed_count}")
    print(f"Fallidos: {failed_count}")

def main():
    """Función principal"""
    print("=== PROCESADOR DE ARCHIVOS ZIP PENDIENTES ===")
    
    # 1. Verificar servicios
    if not check_services():
        print("\n❌ No todos los servicios están activos.")
        print("Por favor, inicia todos los servicios antes de continuar.")
        print("Ejecuta: python start_all_services.py")
        return
    
    print("\n✅ Todos los servicios están activos")
    
    # 2. Procesar archivos
    process_all_pending_files()
    
    print("\n✅ Procesamiento completado")

if __name__ == "__main__":
    main()
