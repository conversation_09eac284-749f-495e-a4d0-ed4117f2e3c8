#!/usr/bin/env python3
"""
Debug script to identify remaining issues with client_id fix
"""

import os
import sys
import requests
import json
from datetime import datetime

# Set the database URL environment variable
os.environ['DATABASE_URL'] = 'postgresql://cufe_user:cufe_password@localhost:5432/cufe_db'

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.database.models import EmailRecord, CUFERecord, Client, User
from shared.database.connection import get_db

def check_database_detailed():
    """Check the detailed state of the database"""
    print("🔍 DETAILED DATABASE ANALYSIS")
    print("=" * 50)
    
    # Get database session
    db = next(get_db())
    
    try:
        # Check clients
        print("\n📊 CLIENTS:")
        clients = db.query(Client).all()
        for client in clients:
            print(f"  ID: {client.id}, Client ID: {client.client_id}, Company: {client.company_name}")
        
        # Check users with their client associations
        print("\n👥 USERS WITH CLIENT INFO:")
        users = db.query(User).all()
        for user in users:
            client = db.query(Client).filter(Client.id == user.client_id).first()
            client_name = client.company_name if client else "NO CLIENT"
            print(f"  ID: {user.id}, Username: {user.username}, Client ID: {user.client_id} ({client_name})")
        
        # Check ALL email records
        print("\n📧 ALL EMAIL RECORDS:")
        email_records = db.query(EmailRecord).order_by(EmailRecord.id.desc()).all()
        print(f"  Total email records: {len(email_records)}")
        for record in email_records:
            print(f"    ID: {record.id}, Email: {record.email_id[:30]}..., Client ID: {record.client_id}, Status: {record.processing_status}")
        
        # Check ALL CUFE records with their email associations
        print("\n🧾 ALL CUFE RECORDS WITH EMAIL INFO:")
        cufe_query = db.query(CUFERecord, EmailRecord.client_id, EmailRecord.email_id).join(EmailRecord).order_by(CUFERecord.id.desc())
        cufe_records = cufe_query.all()
        print(f"  Total CUFE records: {len(cufe_records)}")
        for cufe_record, client_id, email_id in cufe_records:
            print(f"    CUFE: {cufe_record.cufe_value[:20]}..., Issuer: {cufe_record.issuer_name}")
            print(f"          Email: {email_id[:30]}..., Client ID: {client_id}")
        
        # Count records by client_id
        print("\n📈 RECORDS BY CLIENT:")
        for client in clients:
            email_count = db.query(EmailRecord).filter(EmailRecord.client_id == client.id).count()
            cufe_count = db.query(CUFERecord).join(EmailRecord).filter(EmailRecord.client_id == client.id).count()
            print(f"  Client {client.id} ({client.company_name}): {email_count} emails, {cufe_count} CUFEs")
        
        # Check records without client_id
        print("\n⚠️  ORPHANED RECORDS:")
        emails_without_client = db.query(EmailRecord).filter(EmailRecord.client_id.is_(None)).count()
        print(f"  Email records without client_id: {emails_without_client}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False
    finally:
        db.close()

def test_api_endpoints():
    """Test all relevant API endpoints"""
    print("\n🔗 API ENDPOINT TESTING")
    print("=" * 50)
    
    try:
        # Test login
        print("\n1. Testing login...")
        login_response = requests.post(
            "http://localhost:8000/auth/login",
            json={"username": "testuser", "password": "password123"},
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"   Response: {login_response.text}")
            return False
        
        login_data = login_response.json()
        token = login_data["access_token"]
        user_info = login_data["user"]
        client_info = login_data["client"]
        
        print(f"✅ Login successful")
        print(f"   User: {user_info['username']} (ID: {user_info['id']})")
        print(f"   Client: {client_info['company_name']} (ID: {user_info['client_id']})")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test /auth/me endpoint
        print("\n2. Testing /auth/me...")
        me_response = requests.get("http://localhost:8000/auth/me", headers=headers, timeout=10)
        if me_response.status_code == 200:
            me_data = me_response.json()
            print(f"✅ /auth/me successful")
            print(f"   User ID: {me_data['user']['id']}, Client ID: {me_data['user']['client_id']}")
        else:
            print(f"❌ /auth/me failed: {me_response.status_code}")
        
        # Test CUFE endpoint
        print("\n3. Testing CUFE endpoint...")
        cufe_response = requests.get(
            "http://localhost:8000/cufe/?skip=0&limit=10",
            headers=headers,
            timeout=10
        )
        
        if cufe_response.status_code == 200:
            cufe_data = cufe_response.json()
            print(f"✅ CUFE API successful")
            print(f"   Total records: {cufe_data['total']}")
            print(f"   Records returned: {len(cufe_data['records'])}")
            print(f"   Response size: {len(cufe_response.text)} bytes")
            
            if cufe_data['records']:
                print("   Sample records:")
                for i, record in enumerate(cufe_data['records'][:2]):
                    print(f"     {i+1}. CUFE: {record['cufe_value'][:20]}...")
                    print(f"        Issuer: {record.get('issuer_name', 'N/A')}")
            else:
                print("   ⚠️  No records found - this is the issue!")
        else:
            print(f"❌ CUFE API failed: {cufe_response.status_code}")
            print(f"   Response: {cufe_response.text}")
        
        # Test sync email processing (if user wants to test)
        print("\n4. Email processing test available")
        print("   To test email processing, you can call:")
        print("   POST http://localhost:8000/process-emails-sync")
        print("   with your email credentials and the Authorization header")
        
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def suggest_next_steps():
    """Suggest next debugging steps"""
    print("\n🔧 SUGGESTED NEXT STEPS")
    print("=" * 50)
    
    print("\n1. If no CUFE records are found for the client:")
    print("   - Check if email processing is actually working")
    print("   - Try the sync endpoint: POST /process-emails-sync")
    print("   - Check Docker logs: docker logs cufe_api_service")
    
    print("\n2. If CUFE records exist but have wrong client_id:")
    print("   - Run: python fix_database.py")
    print("   - Check database manually with psql")
    
    print("\n3. If email processing fails:")
    print("   - Check mailbox service logs: docker logs cufe_mailbox_service")
    print("   - Verify email credentials are correct")
    print("   - Check network connectivity")
    
    print("\n4. If authentication issues:")
    print("   - Verify JWT token is valid")
    print("   - Check user-client associations in database")

if __name__ == "__main__":
    print("🐛 CLIENT_ID FIX DEBUGGING TOOL")
    print("=" * 60)
    
    # Check database state
    print("\nStep 1: Database Analysis")
    db_success = check_database_detailed()
    
    # Test API endpoints
    print("\nStep 2: API Testing")
    api_success = test_api_endpoints()
    
    # Provide suggestions
    suggest_next_steps()
    
    if db_success and api_success:
        print("\n✅ Debugging completed - check the output above for issues")
    else:
        print("\n❌ Some debugging steps failed - check the errors above")
