#!/usr/bin/env python3
"""
Test and fix existing CUFE records to have proper client_id associations
"""

import os
import sys
import requests

# Set the database URL environment variable
os.environ['DATABASE_URL'] = 'postgresql://cufe_user:cufe_password@localhost:5432/cufe_db'

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.database.models import EmailRecord, CUFERecord, Client, User
from shared.database.connection import get_db

def check_and_fix_existing_records():
    """Check existing records and fix client_id associations"""
    print("🔍 CHECKING AND FIXING EXISTING RECORDS")
    print("=" * 50)
    
    # Get database session
    db = next(get_db())
    
    try:
        # Get the test user and client info
        test_user = db.query(User).filter(User.username == 'testuser').first()
        if not test_user:
            print("❌ Test user not found")
            return False
        
        print(f"✅ Found test user: {test_user.username} (ID: {test_user.id}, Client ID: {test_user.client_id})")
        
        # Check all email records
        print("\n📧 CHECKING EMAIL RECORDS:")
        all_email_records = db.query(EmailRecord).all()
        print(f"  Total email records: {len(all_email_records)}")
        
        # Count records without client_id
        records_without_client = db.query(EmailRecord).filter(EmailRecord.client_id.is_(None)).all()
        print(f"  Records without client_id: {len(records_without_client)}")
        
        # Update records without client_id to use the test user's client_id
        if records_without_client:
            print(f"\n🔧 FIXING {len(records_without_client)} EMAIL RECORDS:")
            for record in records_without_client:
                print(f"  Updating email record {record.id} (Email: {record.email_id[:30]}...)")
                record.client_id = test_user.client_id
            
            db.commit()
            print(f"✅ Updated {len(records_without_client)} email records with client_id: {test_user.client_id}")
        
        # Check CUFE records
        print("\n🧾 CHECKING CUFE RECORDS:")
        all_cufe_records = db.query(CUFERecord).all()
        print(f"  Total CUFE records: {len(all_cufe_records)}")
        
        # Check CUFE records with their email associations
        cufe_with_client = db.query(CUFERecord, EmailRecord.client_id).join(EmailRecord).all()
        print(f"  CUFE records with client associations: {len(cufe_with_client)}")
        
        for cufe_record, client_id in cufe_with_client:
            print(f"    CUFE: {cufe_record.cufe_value[:20]}..., Client ID: {client_id}")
        
        # Check records for the test user's client
        test_client_cufe = db.query(CUFERecord).join(EmailRecord).filter(
            EmailRecord.client_id == test_user.client_id
        ).all()
        print(f"  CUFE records for test user's client: {len(test_client_cufe)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def test_api_after_fix():
    """Test the API after fixing the records"""
    print("\n🔗 TESTING API AFTER FIX")
    print("=" * 50)
    
    try:
        # Login to get token
        login_response = requests.post(
            "http://localhost:8000/auth/login",
            json={"username": "testuser", "password": "password123"},
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        login_data = login_response.json()
        token = login_data["access_token"]
        
        print(f"✅ Login successful")
        
        # Test CUFE endpoint
        headers = {"Authorization": f"Bearer {token}"}
        cufe_response = requests.get(
            "http://localhost:8000/cufe/?skip=0&limit=10",
            headers=headers,
            timeout=10
        )
        
        if cufe_response.status_code == 200:
            cufe_data = cufe_response.json()
            print(f"✅ CUFE API successful")
            print(f"   Total records: {cufe_data['total']}")
            print(f"   Records returned: {len(cufe_data['records'])}")
            
            if cufe_data['records']:
                print("   🎉 CUFE records found!")
                for i, record in enumerate(cufe_data['records'][:3]):
                    print(f"     {i+1}. CUFE: {record['cufe_value'][:20]}...")
                    print(f"        Issuer: {record.get('issuer_name', 'N/A')}")
                return True
            else:
                print("   ⚠️  Still no records found")
                return False
        else:
            print(f"❌ CUFE API failed: {cufe_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 TESTING AND FIXING EXISTING RECORDS")
    print("=" * 60)
    
    # Check and fix existing records
    fix_success = check_and_fix_existing_records()
    
    if fix_success:
        # Test API after fix
        api_success = test_api_after_fix()
        
        if api_success:
            print("\n🎉 SUCCESS! CUFE records are now visible!")
            print("The frontend should now show the processed invoices.")
        else:
            print("\n⚠️  Records were fixed but API still returns empty results.")
            print("This might indicate that no CUFE records exist in the database.")
    else:
        print("\n❌ Failed to fix existing records")
