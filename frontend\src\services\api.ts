import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { LoginRequest, LoginResponse, User, Client } from '../types/auth'

// API Base URL - adjust based on environment
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response: AxiosResponse<LoginResponse> = await apiClient.post('/auth/login', credentials)
    return response.data
  },

  getCurrentUser: async (token?: string): Promise<{ user: User; client: Client }> => {
    const headers = token ? { Authorization: `Bearer ${token}` } : {}
    const response: AxiosResponse<{ user: User; client: Client }> = await apiClient.get('/auth/me', { headers })
    return response.data
  },
}

// CUFE API types
export interface Address {
  street_address?: string
  additional_address_line?: string
  city?: string
  state_province?: string
  postal_code?: string
  country?: string
  country_code?: string
}

export interface CompanyInformation {
  company_name?: string
  business_name?: string  // Razón social
  trade_name?: string
  tax_id?: string  // NIT, RUT, etc.
  tax_scheme?: string
  legal_entity_type?: string
  phone?: string
  email?: string
  website?: string
  address?: Address
  industry_sector?: string
  registration_number?: string
  registration_authority?: string
}

export interface InvoiceLineItem {
  line_number?: number
  item_name?: string
  item_description?: string
  item_code?: string
  invoiced_quantity?: string
  unit_of_measure?: string
  unit_price?: string

  // Enhanced pricing and tax information
  subtotal_without_vat?: string  // Subtotal sin IVA
  line_extension_amount?: string
  line_tax_amount?: string  // IVA por artículo
  line_tax_inclusive_amount?: string
  allowance_charge_amount?: string
  free_of_charge_indicator?: boolean

  // Additional VAT details per article
  vat_rate?: string  // VAT percentage rate
  vat_amount_per_unit?: string  // VAT amount per individual unit
  tax_category_code?: string  // Tax category classification
  tax_exemption_reason?: string  // If tax exempt, the reason
}

export interface InvoiceAllowanceCharge {
  charge_indicator: boolean
  allowance_charge_reason_code?: string
  allowance_charge_reason?: string
  multiplier_factor_numeric?: string
  amount?: string
  base_amount?: string
  tax_category?: string
  tax_amount?: string
}

export interface InvoicePaymentTerm {
  payment_means_code?: string
  payment_due_date?: string
  payment_terms_note?: string
  settlement_period_measure?: string
  settlement_period_unit?: string
  settlement_discount_percent?: string
  penalty_surcharge_percent?: string
  amount?: string
}

export interface CUFERecord {
  cufe_value: string
  email_id: string
  reception_date: string
  xml_file_path: string
  pdf_file_path?: string
  processed_date: string

  // Basic invoice information
  issuer_name?: string
  document_number?: string
  issue_date?: string
  total_amount?: string

  // Enhanced tax and monetary details
  tax_exclusive_amount?: string
  tax_inclusive_amount?: string
  allowance_total_amount?: string
  charge_total_amount?: string
  prepaid_amount?: string
  payable_amount?: string

  // Tax breakdown details
  total_tax_amount?: string
  iva_amount?: string
  rete_fuente_amount?: string
  rete_iva_amount?: string
  rete_ica_amount?: string

  // Additional invoice details
  due_date?: string
  currency_code?: string
  invoice_type_code?: string
  accounting_cost?: string

  // Company information - comprehensive business entity data
  issuer_company?: CompanyInformation  // Supplier/issuer company details
  customer_company?: CompanyInformation  // Customer/buyer company details

  // Related data collections
  line_items?: InvoiceLineItem[]
  allowance_charges?: InvoiceAllowanceCharge[]
  payment_terms?: InvoicePaymentTerm[]
}

export interface CUFEListResponse {
  records: CUFERecord[]
  total: number
  skip: number
  limit: number
}

// CUFE API
export const cufeApi = {
  getCUFERecords: async (skip = 0, limit = 100): Promise<CUFEListResponse> => {
    const response: AxiosResponse<CUFEListResponse> = await apiClient.get('/cufe/', {
      params: { skip, limit }
    })
    return response.data
  },

  getCUFEById: async (cufeId: string): Promise<CUFERecord> => {
    const response: AxiosResponse<CUFERecord> = await apiClient.get(`/cufe/${cufeId}`)
    return response.data
  },

  deleteCUFE: async (cufeId: string): Promise<{ success: boolean; message: string; deleted_cufe: string; deleted_issuer: string }> => {
    const response: AxiosResponse<{ success: boolean; message: string; deleted_cufe: string; deleted_issuer: string }> = await apiClient.delete(`/cufe/${cufeId}`)
    return response.data
  },
}

// File Processing API types
export interface ProcessEmailsRequest {
  email_host: string
  email_port: number
  email_username: string
  email_password: string
  use_ssl: boolean
  folder: string
  date_filter?: string
  start_date?: Date
  end_date?: Date
  max_emails?: number  // Deprecated: kept for backward compatibility
}

export interface EmailProcessingSummary {
  total_emails_processed: number
  emails_with_zip_attachments: number
  zip_files_downloaded: number
  xml_files_extracted: number
  cufe_records_created: number
  sample_email_senders?: string[]
  sample_email_subjects?: string[]
}

export interface ProcessEmailsResponse {
  success: boolean
  message: string
  status: string
  user: string
  processing_summary?: EmailProcessingSummary
  errors?: string[]
}

export interface FileUploadResponse {
  filename: string
  file_path: string
  message: string
}

// File Processing API
export const fileProcessingApi = {
  processEmails: async (request: ProcessEmailsRequest): Promise<ProcessEmailsResponse> => {
    const response: AxiosResponse<ProcessEmailsResponse> = await apiClient.post('/process-emails', request)
    return response.data
  },

  processEmailsSync: async (request: ProcessEmailsRequest): Promise<ProcessEmailsResponse> => {
    const response: AxiosResponse<ProcessEmailsResponse> = await apiClient.post('/process-emails-sync', request)
    return response.data
  },

  uploadFile: async (file: File): Promise<FileUploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)

    const response: AxiosResponse<FileUploadResponse> = await apiClient.post('/upload-zip', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  processZipFile: async (filePath: string, emailId?: string): Promise<any> => {
    const response = await apiClient.post('/process-zip', {
      file_path: filePath,
      email_id: emailId,
      preserve_structure: true
    })
    return response.data
  },
}

// Excel export
export const excelApi = {
  exportInvoices: async (skip: number = 0, limit: number = 1000): Promise<Blob> => {
    const response = await apiClient.get('/export-excel', {
      params: { skip, limit },
      responseType: 'blob'
    })
    return response.data
  },
}

// Health check
export const healthApi = {
  check: async (): Promise<{ status: string; service: string }> => {
    const response = await apiClient.get('/health')
    return response.data
  },
}

// Admin API types
export interface AdminUser {
  id: number
  username: string
  email: string
  full_name?: string
  client_id: number
  client_name?: string
  is_admin: boolean
  is_active: boolean
  created_date: string
  last_login?: string
}

export interface AdminUserCreate {
  username: string
  email: string
  password: string
  full_name?: string
  client_id: number
  is_admin?: boolean
}

export interface AdminUserUpdate {
  email?: string
  full_name?: string
  is_admin?: boolean
}

export interface AdminUsersResponse {
  users: AdminUser[]
  total: number
  page: number
  per_page: number
  has_next: boolean
  has_prev: boolean
}

export interface LLMUsage {
  id: number
  user_id: number
  username?: string
  client_id: number
  client_name?: string
  provider: string
  model_name: string
  input_tokens: number
  output_tokens: number
  total_tokens: number
  input_cost: string
  output_cost: string
  total_cost: string
  request_type: string
  processing_time_ms?: number
  success: boolean
  error_message?: string
  created_at: string
}

export interface LLMUsageResponse {
  records: LLMUsage[]
  total: number
  page: number
  per_page: number
  has_next: boolean
  has_prev: boolean
}

export interface DeploymentCost {
  id: number
  service_name: string
  cost_type: string
  cost_amount: string
  currency: string
  period_start: string
  period_end: string
  billing_period: string
  resource_usage?: string
  resource_units?: string
  provider?: string
  region?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface DeploymentCostResponse {
  records: DeploymentCost[]
  total: number
  page: number
  per_page: number
  has_next: boolean
  has_prev: boolean
}

export interface AdminDashboardStats {
  total_users: number
  active_users: number
  suspended_users: number
  total_clients: number
  active_clients: number
  llm_requests_today: number
  llm_requests_this_month: number
  llm_cost_today: string
  llm_cost_this_month: string
  total_invoices_processed: number
  invoices_processed_today: number
  invoices_processed_this_month: number
  recent_users: AdminUser[]
  recent_llm_usage: LLMUsage[]
}

export interface AdminResponse {
  success: boolean
  message: string
}

// Admin API
export const adminApi = {
  // User management
  getUsers: async (page = 1, per_page = 50, search?: string, client_id?: number, is_active?: boolean): Promise<AdminUsersResponse> => {
    const params: any = { page, per_page }
    if (search) params.search = search
    if (client_id) params.client_id = client_id
    if (is_active !== undefined) params.is_active = is_active

    const response: AxiosResponse<AdminUsersResponse> = await apiClient.get('/admin/users', { params })
    return response.data
  },

  createUser: async (userData: AdminUserCreate): Promise<AdminResponse> => {
    const response: AxiosResponse<AdminResponse> = await apiClient.post('/admin/users', userData)
    return response.data
  },

  suspendUser: async (userId: number): Promise<AdminResponse> => {
    const response: AxiosResponse<AdminResponse> = await apiClient.put(`/admin/users/${userId}/suspend`)
    return response.data
  },

  activateUser: async (userId: number): Promise<AdminResponse> => {
    const response: AxiosResponse<AdminResponse> = await apiClient.put(`/admin/users/${userId}/activate`)
    return response.data
  },

  updateUser: async (userId: number, userData: AdminUserUpdate): Promise<AdminResponse> => {
    const response: AxiosResponse<AdminResponse> = await apiClient.put(`/admin/users/${userId}`, userData)
    return response.data
  },

  // Cost monitoring
  getLLMCosts: async (
    start_date?: string,
    end_date?: string,
    user_id?: number,
    client_id?: number,
    provider?: string,
    page = 1,
    per_page = 50
  ): Promise<LLMUsageResponse> => {
    const params: any = { page, per_page }
    if (start_date) params.start_date = start_date
    if (end_date) params.end_date = end_date
    if (user_id) params.user_id = user_id
    if (client_id) params.client_id = client_id
    if (provider) params.provider = provider

    const response: AxiosResponse<LLMUsageResponse> = await apiClient.get('/admin/costs/llm', { params })
    return response.data
  },

  getDeploymentCosts: async (
    start_date?: string,
    end_date?: string,
    service_name?: string,
    cost_type?: string,
    page = 1,
    per_page = 50
  ): Promise<DeploymentCostResponse> => {
    const params: any = { page, per_page }
    if (start_date) params.start_date = start_date
    if (end_date) params.end_date = end_date
    if (service_name) params.service_name = service_name
    if (cost_type) params.cost_type = cost_type

    const response: AxiosResponse<DeploymentCostResponse> = await apiClient.get('/admin/costs/deployment', { params })
    return response.data
  },

  getDashboardStats: async (): Promise<AdminDashboardStats> => {
    const response: AxiosResponse<AdminDashboardStats> = await apiClient.get('/admin/dashboard/stats')
    return response.data
  },
}

export default apiClient
